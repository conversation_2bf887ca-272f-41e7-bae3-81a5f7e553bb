"""
Sales Return Summary Report Serializers.

This module provides request and response serializers for the sales return summary report API
with proper validation and field mapping.
"""

from rest_framework import serializers
from django.core.exceptions import ValidationError
from datetime import datetime
import uuid


class BaoCaoTongHopHangBanTraLaiRequestSerializer(serializers.Serializer):
    """
    Request serializer for Sales Return Summary Report.
    
    Handles input validation with all fields being optional except date range.
    """
    
    ngay_ct1 = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="Start date for document date range (YYYY-MM-DD)"
    )
    ngay_ct2 = serializers.DateField(
        required=False,
        allow_null=True,
        help_text="End date for document date range (YYYY-MM-DD)"
    )
    
    so_ct1 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Document number start range"
    )
    so_ct2 = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Document number end range"
    )
    
    ma_nvbh = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Sales staff UUID filter"
    )
    ma_kh = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer UUID filter"
    )
    
    nh_kh1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 1 UUID (type=KH1)"
    )
    nh_kh2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 2 UUID (type=KH2)"
    )
    nh_kh3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer group 3 UUID (type=KH3)"
    )
    
    rg_code = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Region code UUID filter"
    )
    
    ma_vt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material UUID filter"
    )
    ma_lvt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material type UUID filter"
    )
    
    ton_kho_yn = serializers.BooleanField(
        required=False,
        default=False,
        help_text="Include inventory flag"
    )
    
    nh_vt1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 1 UUID (type=VT1)"
    )
    nh_vt2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 2 UUID (type=VT2)"
    )
    nh_vt3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 3 UUID (type=VT3)"
    )
    
    ma_kho = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Warehouse UUID filter"
    )
    ma_unit = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Unit UUID filter"
    )
    
    loai_du_lieu = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Data type filter"
    )
    group_by = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=50,
        help_text="Grouping option"
    )
    
    ma_gd = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Transaction UUID filter"
    )
    tk_vt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Item account UUID filter"
    )
    tk_dt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Revenue account UUID filter"
    )
    tk_gv = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="COGS account UUID filter"
    )
    
    ma_lo = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Lot UUID filter"
    )
    ma_vi_tri = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Location UUID filter"
    )
    
    dien_giai = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Description filter"
    )
    
    mau_bc = serializers.ChoiceField(
        choices=[('10', 'Quantity only'), ('20', 'Quantity and value'), ('30', 'Quantity, value, and foreign currency')],
        required=True,
        help_text="Report template: 10=Quantity only, 20=Quantity and value, 30=Quantity, value, and foreign currency"
    )
    
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Data analysis structure"
    )

    def validate(self, data):
        """
        Validate the entire request data.
        
        Parameters
        ----------
        data : dict
            Request data
            
        Returns
        -------
        dict
            Validated data
            
        Raises
        ------
        ValidationError
            If validation fails
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')
        
        if ngay_ct1 and ngay_ct2 and ngay_ct1 > ngay_ct2:
            raise serializers.ValidationError(
                "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
            )
        
        so_ct1 = data.get('so_ct1', '').strip()
        so_ct2 = data.get('so_ct2', '').strip()
        
        if so_ct1 and so_ct2 and so_ct1 > so_ct2:
            raise serializers.ValidationError(
                "Document number start (so_ct1) must be less than or equal to end (so_ct2)"
            )
        
        return data

    def to_internal_value(self, data):
        """
        Convert input data to internal representation.
        Handle empty strings for optional fields.
        """
        uuid_fields = [
            'ma_nvbh', 'ma_kh', 'nh_kh1', 'nh_kh2', 'nh_kh3', 'rg_code',
            'ma_vt', 'ma_lvt', 'nh_vt1', 'nh_vt2', 'nh_vt3',
            'ma_kho', 'ma_unit', 'ma_gd', 'tk_vt', 'tk_dt', 'tk_gv',
            'ma_lo', 'ma_vi_tri'
        ]
        
        for field in uuid_fields:
            if field in data and data[field] == '':
                data[field] = None
        
        return super().to_internal_value(data)


class BaoCaoTongHopHangBanTraLaiResponseSerializer(serializers.Serializer):
    """
    Response serializer for Sales Return Summary Report.

    Handles different field sets based on mau_bc parameter.
    """

    # Basic fields for mau_bc "10" (always present)
    ma_vt = serializers.UUIDField(
        allow_null=True,
        help_text="Material UUID"
    )
    ten_vt = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        help_text="Material name"
    )
    dvt = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        help_text="Unit of measure"
    )
    sl_nhap = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        help_text="Return quantity (renamed from so_luong)"
    )

    # Additional fields for mau_bc "20" and "30" (ERP standard fields)
    ngay_ct = serializers.DateField(
        required=False,
        help_text="Document date"
    )
    so_ct = serializers.CharField(
        required=False,
        help_text="Document number"
    )
    ma_kh = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Customer UUID"
    )
    ma_kho = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Warehouse UUID"
    )

    # Fields for mau_bc "20" and "30"
    he_so = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Conversion factor"
    )
    gia = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Price"
    )
    tien_nhap = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Import amount"
    )
    gia2 = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Price 2 (gia_nt2)"
    )
    tien2 = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Amount 2"
    )
    thue = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Tax amount"
    )
    pt = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Percentage"
    )
    tien_lai = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Profit amount"
    )

    # Additional fields for mau_bc "30"
    gia_nt1 = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Foreign currency price 1"
    )
    tien_nt1 = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Foreign currency amount 1"
    )
    tien_nt2 = serializers.DecimalField(
        max_digits=15,
        decimal_places=4,
        required=False,
        help_text="Foreign currency amount 2"
    )

    def to_representation(self, instance):
        """
        Custom representation to conditionally include fields based on mau_bc.

        For mau_bc "10": Only basic fields (ma_vt, ten_vt, dvt, so_luong)
        For mau_bc "20": Extended fields with sl_nhap instead of so_luong
        For mau_bc "30": All fields with sl_nhap instead of so_luong
        """
        data = super().to_representation(instance)

        mau_bc = self.context.get('mau_bc', '10')

        if mau_bc == "10":
            return {
                'ma_vt': data.get('ma_vt'),
                'ten_vt': data.get('ten_vt'),
                'dvt': data.get('dvt'),
                'sl_nhap': data.get('sl_nhap'),
            }

        result = {
            'ma_vt': data.get('ma_vt'),
            'ten_vt': data.get('ten_vt'),
            'dvt': data.get('dvt'),
            'sl_nhap': data.get('sl_nhap'),  # Use sl_nhap for mau_bc 20/30
            'gia': data.get('gia'),
            'tien_nhap': data.get('tien_nhap'),
            'gia2': data.get('gia2'),
            'tien2': data.get('tien2'),
            'thue': data.get('thue'),
            'pt': data.get('pt'),
            'tien_lai': data.get('tien_lai'),
        }

        if mau_bc == "30":
            result.update({
                'ma_nt': data.get('ma_nt'),
                'gia_nt1': data.get('gia_nt1'),
                'tien_nt1': data.get('tien_nt1'),
                'tien_nt2': data.get('tien_nt2'),
            })

        return result
