"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangModel, which represents the purchase expense receipt  # noqa: E501
in the system.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn  # noqa: F401,
from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class PhieuNhapChiPhiMuaHangModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the PhieuNhapChiPhiMuaHangModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapChiPhiMuaHangModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of PhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.filter(entity_model__slug=entity_slug)


class PhieuNhapChiPhiMuaHangModelManager(Manager):
    """
    A custom defined PhieuNhapChiPhiMuaHangModel Manager that will act as an interface to handle the  # noqa: E501
    PhieuNhapChiPhiMuaHangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom PhieuNhapChiPhiMuaHangModelQueryset.
        """
        return PhieuNhapChiPhiMuaHangModelQueryset(self.model, using=self._db)

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns PhieuNhapChiPhiMuaHangModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of PhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)


class PhieuNhapChiPhiMuaHangModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    This is the main abstract class which the PhieuNhapChiPhiMuaHangModel database will inherit from.  # noqa: E501
    The PhieuNhapChiPhiMuaHangModel inherits functionality from the following MixIns:

        1. :func:`ChungTuMixIn <django_ledger.models._mixins.chung_tu_mixins.ChungTuMixIn>`
        2. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    entity_model : ForeignKey
        The EntityModel this record belongs to.
    ma_kh : ForeignKey
        Customer code.
    ten_kh : CharField
        Customer name.
    ong_ba : CharField
        Contact person name.
    e_mail : EmailField
        Email address.
    tk : ForeignKey
        Account code.
    ma_tt : ForeignKey
        Payment term reference to HanThanhToanModel.
    dien_giai : TextField
        Description.
    ma_ngv : CharField
        Assignee code.
    unit_id : ForeignKey
        Unit reference to EntityUnitModel.
    # Document fields provided by ChungTuMixIn:
    # i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu
    ma_ct : CharField
        Document type code.
    so_ct0 : CharField
        Original document number.
    so_ct2 : CharField
        Secondary document number.
    ngay_ct0 : DateField
        Original document date.
    ma_nt : ForeignKey
        Currency code.
    ty_gia : DecimalField
        Exchange rate.
    status : CharField
        Status.
    transfer_yn : BooleanField
        Transfer status.
    ma_gd : CharField
        Transaction code.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    t_cp_nt : DecimalField
        Total expense in foreign currency.
    t_cp : DecimalField
        Total expense.
    t_thue_nt : DecimalField
        Total tax in foreign currency.
    t_thue : DecimalField
        Total tax.
    t_tt_nt : DecimalField
        Total payment in foreign currency.
    t_tt : DecimalField
        Total payment.
    created_at : DateTimeField
        Creation date.
    created_by : CharField
        Created by.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity'),
        related_name='phieu_nhap_chi_phi_mua_hang',
    )

    # Customer information
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        related_name='phieu_nhap_chi_phi_mua_hang',
    )
    ten_kh = models.CharField(max_length=255, verbose_name=_('Tên khách hàng'))
    ong_ba = models.CharField(max_length=255, verbose_name=_('Ông/Bà'))
    e_mail = models.EmailField(blank=True, null=True, verbose_name=_('Email'))
    # Account information
    tk = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản'),
        related_name='phieu_nhap_chi_phi_mua_hang_tk',
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thanh toán'),
        related_name='phieu_nhap_chi_phi_mua_hang_ma_tt',
        null=True,
        blank=True,
    )
    dien_giai = models.TextField(verbose_name=_('Diễn giải'))
    # Document information
    ma_ngv = models.CharField(max_length=50, verbose_name=_('Mã người giao việc'))
    unit_id = models.ForeignKey(
        'django_ledger.EntityUnitModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị'),
        related_name='phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    # Document fields are provided by ChungTuMixIn:
    # i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct, chung_tu
    ma_ct = models.CharField(
        max_length=50,
        verbose_name=_('Mã chứng từ'),
        null=True,
        blank=True,
    )
    so_ct0 = models.CharField(max_length=50, verbose_name=_('Số chứng từ 0'))
    so_ct2 = models.CharField(max_length=50, verbose_name=_('Số chứng từ 2'))
    ngay_ct0 = models.DateField(verbose_name=_('Ngày chứng từ 0'))
    # Currency information
    ma_nt = models.ForeignKey(
        'django_ledger.NgoaiTeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã ngoại tệ'),
        related_name='phieu_nhap_chi_phi_mua_hang',
        null=True,
    )
    ty_gia = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tỷ giá')
    )

    # Status information
    status = models.CharField(max_length=2, verbose_name=_('Trạng thái'))
    transfer_yn = models.BooleanField(default=False, verbose_name=_('Đã chuyển'))
    ma_gd = models.CharField(max_length=10, verbose_name=_('Mã giao dịch'))
    # Amount information
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
    )
    t_tien = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tổng tiền')
    )
    t_cp_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng chi phí ngoại tệ'),
    )
    t_cp = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tổng chi phí')
    )
    t_thue_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng thuế ngoại tệ'),
    )
    t_thue = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tổng thuế')
    )
    t_tt_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng thanh toán ngoại tệ'),
    )
    t_tt = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tổng thanh toán')
    )

    objects = PhieuNhapChiPhiMuaHangModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Phiếu nhập chi phí mua hàng')
        verbose_name_plural = _('Phiếu nhập chi phí mua hàng')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['tk']),
            models.Index(fields=['ma_nt']),
            models.Index(fields=['ma_tt']),
            models.Index(fields=['status']),
            models.Index(fields=['unit_id']),
        ]
        ordering = [
            '-created',
        ]  # Removed -ngay_ct since it's now in ChungTuItem
        # Removed unique_together with so_ct since it's now in ChungTuItem

    def __str__(self):  # noqa: C901
        return f'{self.so_ct} - {self.ten_kh}'


class PhieuNhapChiPhiMuaHangModel(PhieuNhapChiPhiMuaHangModelAbstract):
    """
    Base PhieuNhapChiPhiMuaHangModel Implementation
    """

    class Meta(PhieuNhapChiPhiMuaHangModelAbstract.Meta):
        abstract = False
        db_table = 'phieu_nhap_chi_phi_mua_hang'
