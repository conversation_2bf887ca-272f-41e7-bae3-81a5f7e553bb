"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don <PERSON>a <PERSON>u (Purchase Service Invoice) ViewSet.
"""

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from django_ledger.api.decorators import api_exception_handler
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuSerializer
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuService
from django_ledger.api.views.common import ERPPagination


class HoaDonMuaDichVuViewSet(viewsets.ModelViewSet):
    """
    ViewSet for HoaDonMuaDichVuModel with child relationship.
    """
    queryset = HoaDonMuaDichVuModel.objects.all()
    serializer_class = HoaDonMuaDichVuSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = ERPPagination
    lookup_field = 'uuid'
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['ma_gd', 'status', 'ma_kh']
    search_fields = ['ma_gd', 'so_ct', 'dien_giai', 'ong_ba']
    ordering_fields = ['ma_gd', 'so_ct', 'created', 'updated']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = HoaDonMuaDichVuService()

    def get_queryset(self):
        """
        Get the queryset for the view, filtered by entity_slug.
        """
        entity_slug = self.kwargs.get('entity_slug')
        if entity_slug:
            return self.service.list(entity_slug=entity_slug)
        return super().get_queryset()

    def get_serializer_context(self):
        """
        Add entity_slug to serializer context.
        """
        context = super().get_serializer_context()
        context['entity_slug'] = self.kwargs.get('entity_slug')
        return context

    def perform_create(self, serializer):
        """
        Perform create operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        validated_data = serializer.validated_data
        # Note: HoaDonMuaDichVuModel doesn't have created_by field
        # It uses CreateUpdateMixIn which only has 'created' and 'updated' fields

        instance = self.service.create(
            entity_slug=entity_slug,
            data=validated_data
        )

        # Set the instance on the serializer to ensure proper response
        serializer.instance = instance

    def perform_update(self, serializer):
        """
        Perform update operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')
        validated_data = serializer.validated_data
        validated_data['updated_by'] = self.request.user.username

        instance = self.service.update(
            entity_slug=entity_slug,
            uuid=uuid,
            data=validated_data
        )

        # Set the instance on the serializer to ensure proper response
        serializer.instance = instance

    def perform_destroy(self, instance):
        """
        Perform destroy operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = instance.uuid

        self.service.delete(entity_slug=entity_slug, uuid=uuid)

    @api_exception_handler
    def create(self, request, entity_slug=None):
        """
        Creates a new HoaDonMuaDichVuModel instance.
        """
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data

            # Note: HoaDonMuaDichVuModel doesn't have created_by field
            # It uses CreateUpdateMixIn which only has 'created' and 'updated' fields

            # Create the HoaDonMuaDichVuModel instance using service
            instance = self.service.create(
                entity_slug=entity_slug,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=instance.uuid)

            # Create serializer with proper context
            serializer = self.get_serializer(instance)

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def update(self, request, entity_slug=None, pk=None):
        """
        Updates an existing HoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'HoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user.username

            # Update the HoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

            # Create serializer with proper context
            serializer = self.get_serializer(instance)

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def partial_update(self, request, entity_slug=None, pk=None):
        """
        Partially updates an existing HoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'HoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user.username

            # Update the HoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

            # Create serializer with proper context
            serializer = self.get_serializer(instance)

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def destroy(self, request, entity_slug=None, pk=None):
        """
        Deletes a HoaDonMuaDichVuModel instance.
        """
        success = self.service.delete(entity_slug=entity_slug, uuid=pk)

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(
            {'detail': 'HoaDonMuaDichVu not found.'},
            status=status.HTTP_404_NOT_FOUND
        )

    @api_exception_handler
    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific HoaDonMuaDichVu instance.
        """
        return super().retrieve(request, *args, **kwargs)
