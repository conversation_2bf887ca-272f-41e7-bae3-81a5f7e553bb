"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua <PERSON>ch <PERSON>u (Purchase Service Invoice) ViewSet.
"""

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuSerializer
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuService
from django_ledger.api.views.common import ERPPagination


class HoaDonMuaDichVuViewSet(viewsets.ModelViewSet):
    """
    ViewSet for HoaDonMuaDichVuModel with child relationship.
    """
    queryset = HoaDonMuaDichVuModel.objects.all()
    serializer_class = HoaDonMuaDichVuSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = ERPPagination
    lookup_field = 'uuid'
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['ma_gd', 'status', 'ma_kh', 'ngay_ct']
    search_fields = ['ma_gd', 'so_ct', 'dien_giai', 'ong_ba']
    ordering_fields = ['ma_gd', 'so_ct', 'ngay_ct', 'created', 'updated']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = HoaDonMuaDichVuService()

    def get_queryset(self):
        """
        Get the queryset for the view, filtered by entity_slug.
        """
        entity_slug = self.kwargs.get('entity_slug')
        if entity_slug:
            return self.service.list(entity_slug=entity_slug)
        return super().get_queryset()

    def get_serializer_context(self):
        """
        Add child data to serializer context.
        """
        context = super().get_serializer_context()

        # Add child data to context for serializer use with error handling
        entity_slug = self.kwargs.get('entity_slug')
        if entity_slug and (self.action == 'retrieve' or self.action == 'list'):
            try:
                # Get UUID of current object if in retrieve action
                parent_uuid = self.kwargs.get('uuid') if self.action == 'retrieve' else None

                # Get child data from service
                child_data = self.service.get_child_data(
                    entity_slug=entity_slug,
                    parent_uuid=parent_uuid,
                    user_model=self.request.user
                )

                # Add to context for serializer use
                context['child_data'] = child_data
            except Exception as e:
                # Log error but don't fail the request
                print(f"Error getting child data: {e}")
                context['child_data'] = {}

        return context

    def perform_create(self, serializer):
        """
        Perform create operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        validated_data = serializer.validated_data
        # Note: HoaDonMuaDichVuModel doesn't have created_by field
        # It uses CreateUpdateMixIn which only has 'created' and 'updated' fields

        instance = self.service.create(
            entity_slug=entity_slug,
            data=validated_data
        )

        # Set the instance on the serializer to ensure proper response
        serializer.instance = instance

    def perform_update(self, serializer):
        """
        Perform update operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = self.kwargs.get('uuid')
        validated_data = serializer.validated_data
        validated_data['updated_by'] = self.request.user.username

        instance = self.service.update(
            entity_slug=entity_slug,
            uuid=uuid,
            data=validated_data
        )

        # Set the instance on the serializer to ensure proper response
        serializer.instance = instance

    def perform_destroy(self, instance):
        """
        Perform destroy operation using the service.
        """
        entity_slug = self.kwargs.get('entity_slug')
        uuid = instance.uuid

        self.service.delete(entity_slug=entity_slug, uuid=uuid)

    def create(self, request, entity_slug=None):
        """
        Creates a new HoaDonMuaDichVuModel instance.
        """
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            user_model = request.user

            # Note: HoaDonMuaDichVuModel doesn't have created_by field
            # It uses CreateUpdateMixIn which only has 'created' and 'updated' fields

            # Create the HoaDonMuaDichVuModel instance using service
            instance = self.service.create(
                entity_slug=entity_slug,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=instance.uuid)

            # Get child data for the created instance with error handling
            try:
                child_data = self.service.get_child_data(
                    entity_slug=entity_slug,
                    parent_uuid=instance.uuid,
                    user_model=user_model
                )
            except Exception as e:
                print(f"Error getting child data in create: {e}")
                child_data = {}

            # Create serializer with proper context including child data
            serializer = self.get_serializer(instance, context={
                'request': request,
                'child_data': child_data
            })

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, entity_slug=None, pk=None):
        """
        Updates an existing HoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'HoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user.username

            # Update the HoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

            # Get child data for the updated instance
            child_data = self.service.get_child_data(
                entity_slug=entity_slug,
                parent_uuid=instance.uuid,
                user_model=request.user
            )

            # Create serializer with proper context including child data
            serializer = self.get_serializer(instance, context={
                'request': request,
                'child_data': child_data
            })

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def partial_update(self, request, entity_slug=None, pk=None):
        """
        Partially updates an existing HoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'HoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            validated_data['updated_by'] = request.user.username

            # Update the HoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

            # Get child data for the updated instance
            child_data = self.service.get_child_data(
                entity_slug=entity_slug,
                parent_uuid=instance.uuid,
                user_model=request.user
            )

            # Create serializer with proper context including child data
            serializer = self.get_serializer(instance, context={
                'request': request,
                'child_data': child_data
            })

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, entity_slug=None, pk=None):
        """
        Deletes a HoaDonMuaDichVuModel instance.
        """
        success = self.service.delete(entity_slug=entity_slug, uuid=pk)

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(
            {'detail': 'HoaDonMuaDichVu not found.'},
            status=status.HTTP_404_NOT_FOUND
        )
