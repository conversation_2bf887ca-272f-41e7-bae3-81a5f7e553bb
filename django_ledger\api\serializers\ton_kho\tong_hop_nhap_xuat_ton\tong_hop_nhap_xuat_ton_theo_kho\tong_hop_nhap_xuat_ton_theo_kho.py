"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializers for Tong Hop Nhap Xuat Ton <PERSON> (Inventory Summary by Warehouse Report).

These serializers handle request validation and response formatting for the
inventory summary report API, ensuring data integrity and consistent responses.
"""

from rest_framework import serializers
from datetime import date


class TongHopNhapXuatTonTheoKhoRequestSerializer(serializers.Serializer):
    """
    Serializer for Tong Hop Nhap Xuat Ton Theo <PERSON>ho request parameters.
    Validates all filter parameters from the original cURL request.

    Based on enterprise ERP experience, this includes comprehensive filtering
    capabilities for warehouse-based inventory reporting.
    """

    # Date range filters (required)
    ngay_ct1 = serializers.DateField(
        required=True,
        help_text="Start date for the report period (YYYY-MM-DD)"
    )
    ngay_ct2 = serializers.DateField(
        required=True,
        help_text="End date for the report period (YYYY-MM-DD)"
    )

    # Warehouse and material filters
    ma_kho = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="Warehouse UUIDs array (e.g., ['uuid1','uuid2'])"
    )
    ma_vt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material UUID filter"
    )

    # Account filters
    tk_vt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material account UUID filter"
    )
    tk_ht = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="System account UUID filter"
    )
    tk_gv = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Cost account UUID filter"
    )

    # Location and batch filters
    ma_lo = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Lot/batch UUID filter"
    )
    ma_vi_tri = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Location UUID filter"
    )

    # Material category and grouping
    ma_lvt = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material category UUID filter"
    )
    ton_kho_yn = serializers.BooleanField(
        required=False,
        default=True,
        help_text="Include only inventory-tracked materials"
    )

    # Material group filters
    nh_vt1 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 1 UUID filter"
    )
    nh_vt2 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 2 UUID filter"
    )
    nh_vt3 = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Material group 3 UUID filter"
    )

    # Unit and reporting parameters
    ma_unit = serializers.UUIDField(
        required=False,
        allow_null=True,
        help_text="Unit UUID filter"
    )
    loai_du_lieu = serializers.IntegerField(
        required=False,
        default=1,
        help_text="Data type (1=Standard)"
    )
    mau_bc = serializers.IntegerField(
        required=False,
        default=20,
        help_text="Report template number"
    )
    dvt = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        allow_empty=True,
        help_text="Unit of measure UUIDs array (e.g., ['uuid1','uuid2'])"
    )
    vt_ton = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Inventory balance filter: blank=all, 1=ton_cuoi!=0, 2=ton_cuoi=0"
    )
    group_by = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Grouping criteria: blank=none, 1=ma_lvt, 2=nh_vt1, 3=nh_vt2, 4=nh_vt3, 5=tk_gv"
    )
    ps_dc = serializers.CharField(
        required=False,
        default="1",
        max_length=10,
        help_text="Debit/Credit flag"
    )
    data_analysis_struct = serializers.CharField(
        required=False,
        allow_blank=True,
        max_length=255,
        help_text="Data analysis structure"
    )

    def validate(self, data):
        """
        Validate the date range and other business rules.
        """
        ngay_ct1 = data.get('ngay_ct1')
        ngay_ct2 = data.get('ngay_ct2')

        if ngay_ct1 and ngay_ct2 and ngay_ct1 > ngay_ct2:
            raise serializers.ValidationError(
                "Start date (ngay_ct1) must be less than or equal to end date (ngay_ct2)"
            )

        # Validate date range is not too large (performance consideration)
        if ngay_ct1 and ngay_ct2:
            date_diff = (ngay_ct2 - ngay_ct1).days
            if date_diff > 365:
                raise serializers.ValidationError(
                    "Date range cannot exceed 365 days for performance reasons"
                )

        return data


class TongHopNhapXuatTonTheoKhoResponseSerializer(serializers.Serializer):
    """
    Serializer for Tong Hop Nhap Xuat Ton Theo Kho response data.
    Defines all fields that should be returned in the inventory summary report.

    This matches the exact field specification from the original system
    to ensure compatibility and consistent reporting.
    """

    stt = serializers.IntegerField(help_text="Sequential number")
    ma_kho = serializers.CharField(max_length=50, help_text="Warehouse code")
    ma_vt = serializers.CharField(max_length=50, help_text="Material code")
    nhom = serializers.CharField(max_length=50, help_text="Material group/category")
    dvt = serializers.CharField(max_length=50, help_text="Unit of measure")

    # Opening balance
    ton_dau = serializers.DecimalField(
        max_digits=15, decimal_places=3, help_text="Opening balance quantity"
    )
    du_dau = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Opening balance amount"
    )

    # Period movements
    sl_nhap = serializers.DecimalField(
        max_digits=15, decimal_places=3, help_text="Import quantity"
    )
    tien_nhap = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Import amount"
    )
    sl_xuat = serializers.DecimalField(
        max_digits=15, decimal_places=3, help_text="Export quantity"
    )
    tien_xuat = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Export amount"
    )

    # Closing balance
    ton_cuoi = serializers.DecimalField(
        max_digits=15, decimal_places=3, help_text="Closing balance quantity"
    )
    du_cuoi = serializers.DecimalField(
        max_digits=15, decimal_places=2, help_text="Closing balance amount"
    )

    # Descriptive fields
    ten_vt = serializers.CharField(max_length=255, help_text="Material name")
    ten_kho = serializers.CharField(max_length=255, help_text="Warehouse name")
