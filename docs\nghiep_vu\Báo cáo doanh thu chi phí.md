# Đặc Tả <PERSON> Vụ: <PERSON><PERSON><PERSON> Thu Chi Phí

## 1. Tổng Quan Nghiệp Vụ

### 1.1 <PERSON><PERSON><PERSON>ích

<PERSON><PERSON>o cáo doanh thu chi phí là một trong những báo cáo tài chính quan trọng nhất trong hệ thống ERP, cung cấp cái nhìn tổng quan về hiệu quả kinh doanh của doanh nghiệp qua các kỳ báo cáo khác nhau.

### 1.2 Phạm Vi Áp Dụng

- **Đối tượng sử dụng**: <PERSON><PERSON> toán trưởng, <PERSON><PERSON><PERSON><PERSON> đốc tài ch<PERSON>, Ban lãnh đạo
- **Tần suất báo cáo**: <PERSON> (ngày, tuần, tháng, quý, 6 tháng, năm)
- **Đơn vị báo cáo**: <PERSON><PERSON> thể lọc theo đơn vị/chi nhánh cụ thể hoặc tổng hợp toàn công ty

### 1.3 Ý <PERSON><PERSON><PERSON><PERSON>ế Toán

Báo cáo này tuân thủ nguyên tắc kế toán kép và chuẩn mực kế toán Việt Nam:

- **Doanh thu**: Được ghi nhận theo nguyên tắc phát sinh (accrual basis)
- **Chi phí**: Được phân loại theo chức năng và tính chất
- **Lợi nhuận**: Được tính theo công thức: Doanh thu - Chi phí

## 2. Cấu Trúc Dữ Liệu Nguồn

### 2.1 Bảng Chính Liên Quan

#### 2.1.1 Bảng Giao Dịch Kế Toán

```sql
-- django_ledger_transactionmodel: Bảng giao dịch kế toán cơ bản
TABLE django_ledger_transactionmodel (
    uuid CHAR(32) PRIMARY KEY,
    tx_type VARCHAR(10) NOT NULL,  -- 'credit' hoặc 'debit'
    amount DECIMAL(20,2) NOT NULL, -- Số tiền giao dịch
    description VARCHAR(100),      -- Diễn giải
    account_id CHAR(32) NOT NULL,  -- Tài khoản liên quan
    journal_entry_id CHAR(32),     -- Bút toán liên quan
    cleared BOOLEAN DEFAULT FALSE, -- Đã đối soát
    reconciled BOOLEAN DEFAULT FALSE, -- Đã quyết toán
    created DATETIME NOT NULL,
    updated DATETIME
);
```

#### 2.1.2 Bảng Tài Khoản Kế Toán

```sql
-- django_ledger_accountmodel: Danh mục tài khoản kế toán
TABLE django_ledger_accountmodel (
    uuid CHAR(32) PRIMARY KEY,
    code VARCHAR(10) NOT NULL,     -- Mã tài khoản (VD: 511, 632)
    name VARCHAR(100) NOT NULL,    -- Tên tài khoản
    role VARCHAR(25) NOT NULL,     -- Vai trò tài khoản (income_operational, expense_operational, etc.)
    balance_type VARCHAR(6) NOT NULL, -- 'credit' hoặc 'debit'
    locked BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    coa_model_id CHAR(32) NOT NULL, -- Hệ thống tài khoản
    created DATETIME NOT NULL,
    updated DATETIME
);
```

#### 2.1.3 Bảng Bút Toán

```sql
-- django_ledger_journalentrymodel: Bút toán kế toán
TABLE django_ledger_journalentrymodel (
    uuid CHAR(32) PRIMARY KEY,
    description VARCHAR(70),       -- Diễn giải bút toán
    date DATE NOT NULL,           -- Ngày hạch toán
    timestamp DATETIME NOT NULL,   -- Thời điểm ghi sổ
    posted BOOLEAN DEFAULT FALSE,  -- Đã ghi sổ
    locked BOOLEAN DEFAULT FALSE,  -- Đã khóa
    ledger_id CHAR(32) NOT NULL,  -- Sổ cái liên quan
    entity_unit_id CHAR(32),      -- Đơn vị/chi nhánh
    created DATETIME NOT NULL,
    updated DATETIME
);
```

### 2.2 Phân Loại Tài Khoản Theo Vai Trò (Account Roles)

#### 2.2.1 Tài Khoản Doanh Thu

```python
# Các vai trò tài khoản doanh thu
INCOME_ROLES = [
    'income_operational',  # Doanh thu hoạt động kinh doanh (511, 512)
    'income_investing',    # Doanh thu hoạt động đầu tư (515)
    'income_other',        # Thu nhập khác (711, 718)
]
```

#### 2.2.2 Tài Khoản Chi Phí

```python
# Các vai trò tài khoản chi phí
EXPENSE_ROLES = [
    'cogs',                # Chi phí giá vốn (632, 154)
    'expense_operational', # Chi phí hoạt động (641, 642)
    'expense_interest',    # Chi phí tài chính (635)
    'expense_taxes',       # Chi phí thuế và phí khác (821)
]
```

### 2.3 Cơ Chế Tính Toán

#### 2.3.1 Logic Tính Doanh Thu

```sql
-- Doanh thu = Tổng các giao dịch CREDIT của tài khoản doanh thu
SELECT SUM(amount) as doanh_thu
FROM django_ledger_transactionmodel t
JOIN django_ledger_accountmodel a ON t.account_id = a.uuid
JOIN django_ledger_journalentrymodel j ON t.journal_entry_id = j.uuid
WHERE a.role IN ('income_operational', 'income_investing', 'income_other')
  AND t.tx_type = 'credit'
  AND j.posted = TRUE
  AND j.date BETWEEN :start_date AND :end_date;
```

#### 2.3.2 Logic Tính Chi Phí

```sql
-- Chi phí = Tổng các giao dịch DEBIT của tài khoản chi phí
SELECT SUM(amount) as chi_phi
FROM django_ledger_transactionmodel t
JOIN django_ledger_accountmodel a ON t.account_id = a.uuid
JOIN django_ledger_journalentrymodel j ON t.journal_entry_id = j.uuid
WHERE a.role IN ('cogs', 'expense_operational', 'expense_interest', 'expense_taxes')
  AND t.tx_type = 'debit'
  AND j.posted = TRUE
  AND j.date BETWEEN :start_date AND :end_date;
```

## 3. Cấu Trúc API

### 3.1 Endpoint

```
POST /api/entities/{entity_slug}/tai-chinh/phan-tich-bao-cao-tai-chinh-nhieu-don-vi/bao-cao-doanh-thu-chi-phi/
```

### 3.2 Request Parameters

```json
{
  "ngay_ct": "********", // Ngày bắt đầu (YYYYMMDD)
  "loai_ky": "3", // Loại kỳ: 1=ngày, 2=tuần, 3=tháng, 4=quý, 5=6tháng, 6=năm
  "so_ky": 12, // Số kỳ báo cáo
  "ma_unit": "uuid-string", // UUID đơn vị (tùy chọn)
  "id_maubc": "uuid-string", // UUID mẫu báo cáo
  "mau_bc": 20 // Số hiệu mẫu báo cáo
}
```

### 3.3 Response Structure

```json
{
  "count": 11,
  "next": null,
  "previous": null,
  "results": [
    {
      "stt": 1,
      "chi_tieu": "Tổng doanh thu",
      "thang_1_2025_tien": *********.0,
      "thang_1_2025_percent": 100.0,
      "thang_2_2025_tien": 0.0,
      "thang_2_2025_percent": 0.0,
      "tong_cong_tien": *********.0,
      "tong_cong_percent": 100.0
    }
  ],
  "metadata": {
    "entity_slug": "company-abc",
    "start_date": "2025-01-01",
    "time_unit": "3",
    "period_count": 12,
    "unit_filter": "uuid-string"
  }
}
```

## 4. Các Chỉ Tiêu Báo Cáo

### 4.1 Nhóm Doanh Thu

1. **Tổng doanh thu** - Tổng hợp tất cả các loại doanh thu
2. **Doanh thu bán hàng và cung cấp dịch vụ** - Doanh thu từ hoạt động chính
3. **Doanh thu hoạt động tài chính** - Thu từ đầu tư, cho vay
4. **Thu nhập khác** - Các khoản thu không thường xuyên

### 4.2 Nhóm Chi Phí

5. **Tổng chi phí** - Tổng hợp tất cả các loại chi phí
6. **Chi phí giá vốn** - Chi phí trực tiếp để sản xuất/mua hàng
7. **Chi phí bán hàng** - Chi phí cho hoạt động bán hàng
8. **Chi phí quản lý doanh nghiệp** - Chi phí quản lý chung
9. **Chi phí tài chính** - Lãi vay, phí ngân hàng
10. **Chi phí khác** - Các khoản chi không thường xuyên

### 4.3 Chỉ Tiêu Tổng Hợp

11. **Lợi nhuận** - Doanh thu trừ đi chi phí

## 5. Câu Hỏi Nghiệp Vụ Cần Xác Nhận

### 5.1 Về Phân Loại Tài Khoản

**Q1**: Việc phân loại tài khoản theo vai trò (role) trong hệ thống có đúng với chuẩn mực kế toán Việt Nam không?

- Hiện tại: `income_operational` cho TK 511, 512
- Cần xác nhận: Có cần phân biệt chi tiết hơn không?

**Q2**: Chi phí giá vốn có nên bao gồm cả chi phí sản xuất kinh doanh dở dang không?

- Hiện tại: Chỉ tính TK 632
- Cần xác nhận: Có tính thêm TK 154 không?

### 5.2 Về Thời Gian Ghi Nhận

**Q3**: Doanh thu và chi phí được ghi nhận theo thời điểm nào?

- Tùy chọn 1: Theo ngày chứng từ (journal_entry.date)
- Tùy chọn 2: Theo ngày ghi sổ (journal_entry.timestamp)
- Hiện tại: Đang dùng journal_entry.timestamp

**Q4**: Có cần lọc theo trạng thái "đã ghi sổ" (posted=True) không?

- Hiện tại: Có lọc posted=True
- Cần xác nhận: Có đúng quy trình không?

### 5.3 Về Đơn Vị Báo Cáo

**Q5**: Khi lọc theo đơn vị (ma_unit), có cần tính cả các giao dịch nội bộ không?

- Hiện tại: Lọc theo entity_slug
- Cần xác nhận: Logic lọc đơn vị có chính xác không?

**Q6**: Có cần hỗ trợ báo cáo hợp nhất nhiều đơn vị không?

- Hiện tại: Chỉ hỗ trợ 1 đơn vị hoặc tất cả
- Cần xác nhận: Có cần chọn nhiều đơn vị cụ thể không?

### 5.4 Về Tính Toán Tỷ Lệ Phần Trăm

**Q7**: Tỷ lệ phần trăm được tính như thế nào?

- Hiện tại: 100% cho tất cả dòng có giá trị > 0
- Cần xác nhận: Có cần tính % so với tổng doanh thu không?

**Q8**: Lợi nhuận âm có hiển thị tỷ lệ phần trăm không?

- Hiện tại: Tính theo công thức (lợi nhuận/doanh thu)\*100
- Cần xác nhận: Cách hiển thị lợi nhuận âm?

### 5.5 Về Kỳ Báo Cáo

**Q9**: Kỳ báo cáo có cần tuân thủ năm tài chính của doanh nghiệp không?

- Hiện tại: Tính theo lịch dương
- Cần xác nhận: Có doanh nghiệp nào dùng năm tài chính khác không?

**Q10**: Có cần hỗ trợ so sánh với cùng kỳ năm trước không?

- Hiện tại: Chỉ hiển thị các kỳ liên tiếp
- Cần xác nhận: Có cần thêm cột so sánh không?

### 5.6 Về Bảo Mật và Phân Quyền

**Q11**: Ai có quyền xem báo cáo này?

- Cần xác nhận: Phân quyền theo vai trò nào?
- Có cần che giấu một số chỉ tiêu với một số người dùng không?

**Q12**: Có cần ghi log khi ai đó xem báo cáo không?

- Cần xác nhận: Yêu cầu audit trail?

### 5.7 Về Hiệu Suất và Tối Ưu

**Q13**: Với dữ liệu lớn, có cần cache kết quả báo cáo không?

- Cần xác nhận: Thời gian chấp nhận được cho việc tạo báo cáo?

**Q14**: Có cần hỗ trợ xuất file Excel/PDF không?

- Hiện tại: Chỉ trả về JSON
- Cần xác nhận: Định dạng xuất file nào?

## 6. Phân Tích Chi Tiết Nghiệp Vụ Kế Toán

### 6.1 Nguyên Tắc Ghi Nhận Doanh Thu

Theo Thông tư 200/2014/TT-BTC và các chuẩn mực kế toán Việt Nam:

#### 6.1.1 Doanh Thu Bán Hàng (TK 511)

- **Thời điểm ghi nhận**: Khi hàng hóa đã được giao và quyền sở hữu đã chuyển giao
- **Điều kiện**: Có hóa đơn, chứng từ giao hàng hợp lệ
- **Xử lý**: Ghi Có TK 511, Ghi Nợ TK 131 (Phải thu khách hàng) hoặc TK 111 (Tiền mặt)

#### 6.1.2 Doanh Thu Cung Cấp Dịch Vụ (TK 512)

- **Thời điểm ghi nhận**: Khi dịch vụ đã được hoàn thành
- **Điều kiện**: Có biên bản nghiệm thu dịch vụ
- **Xử lý**: Ghi Có TK 512, Ghi Nợ TK 131 hoặc TK 111

#### 6.1.3 Doanh Thu Hoạt Động Tài Chính (TK 515)

- **Bao gồm**: Lãi tiền gửi, lãi cho vay, cổ tức được chia
- **Thời điểm ghi nhận**: Theo nguyên tắc phát sinh

### 6.2 Nguyên Tắc Ghi Nhận Chi Phí

#### 6.2.1 Chi Phí Giá Vốn Hàng Bán (TK 632)

- **Bao gồm**: Giá mua hàng hóa, chi phí thu mua, vận chuyển
- **Thời điểm ghi nhận**: Khi hàng hóa được xuất bán
- **Xử lý**: Ghi Nợ TK 632, Ghi Có TK 156 (Hàng hóa)

#### 6.2.2 Chi Phí Bán Hàng (TK 641)

- **Bao gồm**: Lương nhân viên bán hàng, quảng cáo, vận chuyển
- **Thời điểm ghi nhận**: Theo nguyên tắc phát sinh
- **Xử lý**: Ghi Nợ TK 641, Ghi Có các TK liên quan

#### 6.2.3 Chi Phí Quản Lý Doanh Nghiệp (TK 642)

- **Bao gồm**: Lương quản lý, khấu hao, điện nước văn phòng
- **Thời điểm ghi nhận**: Theo nguyên tắc phát sinh
- **Xử lý**: Ghi Nợ TK 642, Ghi Có các TK liên quan

### 6.3 Quy Trình Đóng Sổ Cuối Kỳ

#### 6.3.1 Kết Chuyển Doanh Thu

```sql
-- Kết chuyển doanh thu vào TK 911
Nợ TK 511, 512, 515 (Các TK doanh thu)
Có TK 911 (Xác định kết quả kinh doanh)
```

#### 6.3.2 Kết Chuyển Chi Phí

```sql
-- Kết chuyển chi phí vào TK 911
Nợ TK 911 (Xác định kết quả kinh doanh)
Có TK 632, 641, 642, 635 (Các TK chi phí)
```

#### 6.3.3 Xác Định Lợi Nhuận

```sql
-- Số dư TK 911 = Lợi nhuận (nếu Có) hoặc Lỗ (nếu Nợ)
-- Kết chuyển vào TK 421 (Lợi nhuận chưa phân phối)
```

## 7. Mapping Dữ Liệu Chi Tiết

### 7.1 Bảng Mapping Tài Khoản - Vai Trò

| Mã TK | Tên Tài Khoản                 | Account Role        | Loại Báo Cáo                           |
| ----- | ----------------------------- | ------------------- | -------------------------------------- |
| 511   | Doanh thu bán hàng            | income_operational  | Doanh thu bán hàng và cung cấp dịch vụ |
| 512   | Doanh thu cung cấp dịch vụ    | income_operational  | Doanh thu bán hàng và cung cấp dịch vụ |
| 515   | Doanh thu hoạt động tài chính | income_investing    | Doanh thu hoạt động tài chính          |
| 711   | Thu nhập khác                 | income_other        | Thu nhập khác                          |
| 632   | Giá vốn hàng bán              | cogs                | Chi phí giá vốn                        |
| 641   | Chi phí bán hàng              | expense_operational | Chi phí bán hàng                       |
| 642   | Chi phí quản lý DN            | expense_operational | Chi phí quản lý doanh nghiệp           |
| 635   | Chi phí tài chính             | expense_interest    | Chi phí tài chính                      |
| 811   | Chi phí khác                  | expense_taxes       | Chi phí khác                           |

### 7.2 Công Thức Tính Toán Chi Tiết

```python
# Tổng doanh thu
total_revenue = sum([
    income_operational,  # TK 511, 512
    income_investing,    # TK 515
    income_other        # TK 711, 718
])

# Tổng chi phí
total_expense = sum([
    cogs,                # TK 632
    expense_operational, # TK 641, 642
    expense_interest,    # TK 635
    expense_taxes       # TK 811, 821
])

# Lợi nhuận
profit = total_revenue - total_expense

# Tỷ lệ phần trăm (đề xuất cải tiến)
revenue_percent = (each_revenue_item / total_revenue) * 100
expense_percent = (each_expense_item / total_revenue) * 100
profit_percent = (profit / total_revenue) * 100
```

## 8. Kịch Bản Test và Validation

### 8.1 Test Cases Cơ Bản

#### Test Case 1: Báo cáo tháng đơn giản

```json
{
  "ngay_ct": "********",
  "loai_ky": "3",
  "so_ky": 1,
  "ma_unit": null,
  "id_maubc": "uuid-template",
  "mau_bc": 20
}
```

**Expected**: Trả về báo cáo 1 tháng (01/2025) với đầy đủ 11 chỉ tiêu

#### Test Case 2: Báo cáo quý với filter đơn vị

```json
{
  "ngay_ct": "********",
  "loai_ky": "4",
  "so_ky": 4,
  "ma_unit": "unit-uuid-123",
  "id_maubc": "uuid-template",
  "mau_bc": 20
}
```

**Expected**: Trả về báo cáo 4 quý chỉ của đơn vị được chọn

### 8.2 Edge Cases

#### Edge Case 1: Không có dữ liệu

- **Scenario**: Kỳ báo cáo không có giao dịch nào
- **Expected**: Tất cả giá trị = 0, percent = 0

#### Edge Case 2: Chỉ có chi phí, không có doanh thu

- **Scenario**: Doanh thu = 0, chi phí > 0
- **Expected**: Lợi nhuận âm, percent tính được

#### Edge Case 3: Dữ liệu lớn

- **Scenario**: Hàng triệu giao dịch trong kỳ
- **Expected**: Response time < 30 giây

### 8.3 Validation Rules

1. **Ngày tháng**: ngay_ct phải là ngày hợp lệ, không được trong tương lai
2. **Loại kỳ**: loai_ky phải trong khoảng 1-6
3. **Số kỳ**: so_ky phải trong khoảng 1-20
4. **UUID**: ma_unit và id_maubc phải là UUID hợp lệ (nếu có)
5. **Quyền truy cập**: User phải có quyền xem báo cáo của entity

## 9. Đề Xuất Cải Tiến

### 9.1 Tính Năng Bổ Sung

1. **Biểu đồ trực quan**: Thêm API trả về dữ liệu cho biểu đồ
2. **So sánh kỳ**: Thêm khả năng so sánh với cùng kỳ năm trước
3. **Drill-down**: Cho phép xem chi tiết từng tài khoản
4. **Cảnh báo**: Thông báo khi có biến động bất thường

### 9.2 Tối Ưu Hiệu Suất

1. **Indexing**: Tạo index cho các trường thường dùng trong filter
2. **Caching**: Cache kết quả cho các báo cáo đã tạo
3. **Pagination**: Tối ưu phân trang cho dữ liệu lớn
4. **Background job**: Tạo báo cáo nặng ở background

### 9.3 Cải Tiến UX

1. **Preview**: Cho phép xem trước kỳ báo cáo trước khi tạo
2. **Template**: Lưu các thiết lập báo cáo thường dùng
3. **Schedule**: Tự động tạo báo cáo theo lịch
4. **Notification**: Thông báo khi báo cáo hoàn thành

---

_Tài liệu này cần được xem xét và phê duyệt bởi Kế toán trưởng và Giám đốc tài chính trước khi triển khai._
