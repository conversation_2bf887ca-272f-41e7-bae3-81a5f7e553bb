"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Stock Transaction Service - Enterprise-grade inventory transaction management
"""

import logging
from decimal import Decimal
from typing import Dict, Any, Optional, List
from datetime import date, datetime

from django.db import transaction

from django_ledger.models.ton_kho.stock_transaction import StockTransactionModel
from django_ledger.models.ton_kho.warehouse_stock_audit import WarehouseStockAuditModel
from django_ledger.services.base import BaseService
from django_ledger.services.warehouse_stock_audit.warehouse_stock_audit import WarehouseStockAuditService
from django_ledger.repositories.ton_kho.stock_transaction import StockTransactionRepository

logger = logging.getLogger(__name__)


class StockTransactionService(BaseService):
    """
    Service class for StockTransactionModel.

    Enterprise-grade implementation with:
    - Atomic transaction processing
    - Automatic audit trail creation
    - Real-time stock balance calculation
    - Integration with accounting system
    - Performance optimization
    """

    def __init__(self):
        self.warehouse_stock_audit_service = WarehouseStockAuditService()
        self.stock_transaction_repository = StockTransactionRepository()
        super().__init__()

    @transaction.atomic
    def create_stock_transaction(
        self,
        validated_data: Dict[str, Any]
    ) -> StockTransactionModel:
        """
        Create a new stock transaction (audit creation handled by utility layer).

        Following cong_no_creation pattern: Service layer only creates transaction,
        utility layer handles audit creation.

        Parameters
        ----------
        validated_data : Dict[str, Any]
            Validated data containing all required fields:
            - journal_entry: JournalEntryModel
            - warehouse: KhoHangModel
            - product: VatTuModel
            - transaction_type: str (IN, OUT, ADJ, TRF)
            - inbound_quantity: Decimal
            - outbound_quantity: Decimal
            - unit_price: Decimal
            - currency: NgoaiTeModel (optional)
            - lot: LoModel (optional)
            - location: ViTriKhoHangModel (optional)
            - product_name: str (optional)
            - exchange_rate: Decimal (optional, default 1)

        Returns
        -------
        StockTransactionModel
            Created stock transaction instance
        """
        try:
            # Simple field mapping only
            repository_data = {
                'journal_entry': validated_data['journal_entry'],
                'loai_giao_dich': validated_data['transaction_type'],
                'ma_kho': validated_data.get('warehouse'),
                'ma_vt': validated_data['product'],
                'ma_lo': validated_data.get('lot'),
                'ma_vi_tri': validated_data.get('location'),
                'ma_nt': validated_data.get('currency'),
                'ten_vt': validated_data.get('product_name'),
                'px_dd': validated_data.get('exchange_rate', Decimal('1')),
                'pn_tb': validated_data.get('exchange_rate', Decimal('1')),
                'gia_nt': validated_data.get('unit_price_foreign'),
                'gia': validated_data['unit_price'],
                'sl_nhap': validated_data['inbound_quantity'],
                'sl_xuat': validated_data['outbound_quantity'],
                'tien_nt_nhap': validated_data.get('amount_foreign_inbound'),
                'tien_nhap': validated_data.get('amount_inbound'),
                'tien_nt_xuat': validated_data.get('amount_foreign_outbound'),
                'tien_xuat': validated_data.get('amount_outbound')
            }

            # Create stock transaction using repository
            return self.stock_transaction_repository.create_stock_transaction(repository_data)

        except Exception as e:
            logger.error(f"Error creating stock transaction: {str(e)}")
            raise e

    def get_stock_balance(
        self,
        warehouse_id: str,
        product_id: str,
        lot_id: str = None,
        location_id: str = None,
        as_of_date: date = None
    ) -> Dict[str, Any]:
        """
        Get current stock balance for a product in a warehouse.

        Parameters
        ----------
        warehouse_id : str
            Warehouse UUID
        product_id : str
            Product UUID
        lot_id : str, optional
            Lot UUID for batch-specific balance
        location_id : str, optional
            Location UUID for location-specific balance
        as_of_date : date, optional
            Get balance as of specific date

        Returns
        -------
        Dict[str, Any]
            Current stock balance information
        """
        try:
            # Get latest audit record
            filters = {
                'ma_kho__uuid': warehouse_id,
                'ma_vt__uuid': product_id
            }

            if lot_id:
                filters['ma_lo__uuid'] = lot_id

            if location_id:
                filters['ma_vi_tri__uuid'] = location_id

            if as_of_date:
                filters['timestamp__date__lte'] = as_of_date

            latest_audit = WarehouseStockAuditModel.objects.filter(
                **filters
            ).order_by('-timestamp').first()

            if latest_audit:
                balance = latest_audit.balance_after
                value = latest_audit.value_after
                unit_cost = latest_audit.unit_cost_after
                last_updated = latest_audit.timestamp
            else:
                balance = Decimal('0')
                value = Decimal('0')
                unit_cost = Decimal('0')
                last_updated = None

            return {
                'success': True,
                'balance': balance,
                'value': value,
                'unit_cost': unit_cost,
                'last_updated': last_updated,
                'warehouse_id': warehouse_id,
                'product_id': product_id,
                'lot_id': lot_id,
                'location_id': location_id
            }

        except Exception as e:
            logger.error(f"Error getting stock balance: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'balance': Decimal('0'),
                'value': Decimal('0'),
                'unit_cost': Decimal('0')
            }

    def get_stock_movements(
        self,
        warehouse_id: str = None,
        product_id: str = None,
        from_date: date = None,
        to_date: date = None,
        transaction_type: str = None,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        Get stock movement history with filtering options.

        Parameters
        ----------
        warehouse_id : str, optional
            Filter by warehouse UUID
        product_id : str, optional
            Filter by product UUID
        from_date : date, optional
            Filter from date
        to_date : date, optional
            Filter to date
        transaction_type : str, optional
            Filter by transaction type
        limit : int
            Maximum number of records to return

        Returns
        -------
        Dict[str, Any]
            Stock movement history
        """
        try:
            # Start with repository queryset (includes optimized joins)
            queryset = self.stock_transaction_repository.get_queryset()

            # Apply filters
            if warehouse_id:
                queryset = queryset.filter(ma_kho__uuid=warehouse_id)

            if product_id:
                queryset = queryset.filter(ma_vt__uuid=product_id)

            if from_date:
                queryset = queryset.filter(created__date__gte=from_date)

            if to_date:
                queryset = queryset.filter(created__date__lte=to_date)

            if transaction_type:
                queryset = queryset.filter(loai_giao_dich=transaction_type)

            # Order by creation date (newest first) and limit
            transactions = queryset.order_by('-created')[:limit]

            # Format results
            movements = []
            for txn in transactions:
                movements.append({
                    'uuid': str(txn.uuid),
                    'transaction_type': txn.loai_giao_dich,
                    'warehouse': txn.ma_kho.ten_kho if txn.ma_kho else 'N/A',
                    'product': txn.ten_vt,
                    'lot': txn.ma_lo.ten_lo if txn.ma_lo else None,
                    'location': txn.ma_vi_tri.ten_vi_tri if txn.ma_vi_tri else None,
                    'inbound_quantity': txn.sl_nhap,
                    'outbound_quantity': txn.sl_xuat,
                    'net_quantity': txn.net_quantity,
                    'unit_price': txn.gia,
                    'total_value': txn.tien_nhap if txn.sl_nhap > 0 else txn.tien_xuat,
                    'currency': txn.ma_nt.ma_nt if txn.ma_nt else 'N/A',
                    'created': txn.created,
                    'journal_entry_id': str(txn.journal_entry.uuid)
                })

            return {
                'success': True,
                'movements': movements,
                'count': len(movements),
                'total_available': queryset.count()
            }

        except Exception as e:
            logger.error(f"Error getting stock movements: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'movements': [],
                'count': 0
            }

    def get_filtered_stock_transactions(
        self, entity_slug: str, filters: Dict[str, Any], start_date: date, end_date: date
    ) -> List[StockTransactionModel]:
        """
        Get filtered stock transactions for inventory reporting.

        This method implements comprehensive filtering based on all the parameters
        from inventory reports, ensuring accurate inventory reporting.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including warehouse, material, account, etc.
        start_date : date
            Start date for filtering
        end_date : date
            End date for filtering

        Returns
        -------
        List[StockTransactionModel]
            Filtered stock transactions
        """
        try:
            # Start with repository queryset (includes optimized joins)
            queryset = self.stock_transaction_repository.get_for_entity(entity_slug)

            # Apply date range filter
            queryset = queryset.filter(
                journal_entry__timestamp__date__gte=start_date,
                journal_entry__timestamp__date__lte=end_date
            )

            # Warehouse filter (list of UUIDs)
            if filters.get('ma_kho'):
                warehouse_uuids = filters['ma_kho']
                if isinstance(warehouse_uuids, list) and warehouse_uuids:
                    queryset = queryset.filter(ma_kho__uuid__in=warehouse_uuids)

            # Material UUID filter
            if filters.get('ma_vt'):
                material_uuid = filters['ma_vt']
                if material_uuid:
                    queryset = queryset.filter(ma_vt__uuid=material_uuid)

            # Account filters
            if filters.get('tk_vt'):
                account_uuid = filters['tk_vt']
                if account_uuid:
                    queryset = queryset.filter(ma_vt__tk_vt__uuid=account_uuid)

            # Lot/batch filter
            if filters.get('ma_lo'):
                lot_uuid = filters['ma_lo']
                if lot_uuid:
                    queryset = queryset.filter(ma_lo__uuid=lot_uuid)

            # Location filter
            if filters.get('ma_vi_tri'):
                location_uuid = filters['ma_vi_tri']
                if location_uuid:
                    queryset = queryset.filter(ma_vi_tri__uuid=location_uuid)

            # Material category filter
            if filters.get('ma_lvt'):
                category_uuid = filters['ma_lvt']
                if category_uuid:
                    queryset = queryset.filter(ma_vt__ma_lvt__uuid=category_uuid)

            # Inventory tracking flag
            if filters.get('ton_kho_yn') is not None:
                queryset = queryset.filter(ma_vt__ton_kho_yn=filters['ton_kho_yn'])

            # Material group UUID filters
            for i in range(1, 4):
                field_name = f'nh_vt{i}'
                if filters.get(field_name):
                    group_uuid = filters[field_name]
                    if group_uuid:
                        filter_dict = {f'ma_vt__nh_vt{i}__uuid': group_uuid}
                        queryset = queryset.filter(**filter_dict)

            # Unit of measure filter (list of UUIDs)
            if filters.get('dvt'):
                dvt_uuids = filters['dvt']
                if isinstance(dvt_uuids, list) and dvt_uuids:
                    queryset = queryset.filter(ma_vt__dvt__uuid__in=dvt_uuids)

            return list(queryset.order_by('ma_kho__ma_kho', 'ma_vt__ma_vt', 'journal_entry__timestamp'))

        except Exception as e:
            logger.error(f"Error getting filtered stock transactions: {str(e)}", exc_info=True)
            raise Exception(f"Failed to get filtered stock transactions for inventory report: {str(e)}")

    def get_aggregated_stock_movements(
        self, entity_slug: str, filters: Dict[str, Any], start_date: date, end_date: date
    ) -> List[Dict[str, Any]]:
        """
        Get aggregated stock movements for inventory reporting with database-level aggregations.

        This method implements enterprise-grade database-level aggregations for optimal
        performance with large datasets, avoiding Python iteration over QuerySets.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including warehouse, material, account, etc.
        start_date : date
            Start date for filtering
        end_date : date
            End date for filtering

        Returns
        -------
        List[Dict[str, Any]]
            Aggregated stock movements by warehouse-material combination
        """
        try:
            # Start with repository queryset (includes optimized joins)
            queryset = self.stock_transaction_repository.get_for_entity(entity_slug)

            # Apply date range filter
            queryset = queryset.filter(
                journal_entry__timestamp__date__gte=start_date,
                journal_entry__timestamp__date__lte=end_date
            )

            # Apply all the same filters as get_filtered_stock_transactions
            # Warehouse filter (list of UUIDs)
            if filters.get('ma_kho'):
                warehouse_uuids = filters['ma_kho']
                if isinstance(warehouse_uuids, list) and warehouse_uuids:
                    queryset = queryset.filter(ma_kho__uuid__in=warehouse_uuids)

            # Material UUID filter
            if filters.get('ma_vt'):
                material_uuid = filters['ma_vt']
                if material_uuid:
                    queryset = queryset.filter(ma_vt__uuid=material_uuid)

            # Account filters
            if filters.get('tk_vt'):
                account_uuid = filters['tk_vt']
                if account_uuid:
                    queryset = queryset.filter(ma_vt__tk_vt__uuid=account_uuid)

            # Lot/batch filter
            if filters.get('ma_lo'):
                lot_uuid = filters['ma_lo']
                if lot_uuid:
                    queryset = queryset.filter(ma_lo__uuid=lot_uuid)

            # Location filter
            if filters.get('ma_vi_tri'):
                location_uuid = filters['ma_vi_tri']
                if location_uuid:
                    queryset = queryset.filter(ma_vi_tri__uuid=location_uuid)

            # Material category filter
            if filters.get('ma_lvt'):
                category_uuid = filters['ma_lvt']
                if category_uuid:
                    queryset = queryset.filter(ma_vt__ma_lvt__uuid=category_uuid)

            # Inventory tracking flag
            if filters.get('ton_kho_yn') is not None:
                queryset = queryset.filter(ma_vt__ton_kho_yn=filters['ton_kho_yn'])

            # Material group UUID filters
            for i in range(1, 4):
                field_name = f'nh_vt{i}'
                if filters.get(field_name):
                    group_uuid = filters[field_name]
                    if group_uuid:
                        filter_dict = {f'ma_vt__nh_vt{i}__uuid': group_uuid}
                        queryset = queryset.filter(**filter_dict)

            # Unit of measure filter (list of UUIDs)
            if filters.get('dvt'):
                dvt_uuids = filters['dvt']
                if isinstance(dvt_uuids, list) and dvt_uuids:
                    queryset = queryset.filter(ma_vt__dvt__uuid__in=dvt_uuids)

            # Perform database-level aggregation
            from django.db.models import Sum, Value
            from django.db.models.functions import Coalesce

            aggregated_data = queryset.values(
                'ma_kho__uuid',
                'ma_kho__ma_kho',
                'ma_kho__ten_kho',
                'ma_vt__uuid',
                'ma_vt__ma_vt',
                'ma_vt__ten_vt',
                'ma_vt__dvt',
                'ma_vt__ma_lvt',  # Material category (loại vật tư)
                # Add grouping fields for group_by functionality
                'ma_vt__nh_vt1',
                'ma_vt__nh_vt2',
                'ma_vt__nh_vt3',
                'ma_vt__tk_gv'    # Material account (tài khoản giá vốn)
            ).annotate(
                # Aggregate movements using database-level SUM
                sl_nhap_total=Coalesce(Sum('sl_nhap'), Value(Decimal('0'))),
                tien_nhap_total=Coalesce(Sum('tien_nhap'), Value(Decimal('0'))),
                sl_xuat_total=Coalesce(Sum('sl_xuat'), Value(Decimal('0'))),
                tien_xuat_total=Coalesce(Sum('tien_xuat'), Value(Decimal('0')))
            ).order_by('ma_kho__ma_kho', 'ma_vt__ma_vt')

            return list(aggregated_data)

        except Exception as e:
            logger.error(f"Error getting aggregated stock movements: {str(e)}", exc_info=True)
            raise Exception(f"Failed to get aggregated stock movements for inventory report: {str(e)}")
