"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.api.serializers.chung_tu import ChungTuSerializer  # noqa: F401
from django_ledger.api.serializers.customer import CustomerModelSerializer  # noqa: F401
from django_ledger.api.serializers.danh_muc import NgoaiTeSerializer  # noqa: F401,

# Import serializers for foreign key fields
from django_ledger.api.serializers.entity import EntityModelSerializer  # noqa: F401,
from django_ledger.api.serializers.quyen_chung_tu import (  # noqa: F401,
    QuyenChungTuListSerializer,
)
from django_ledger.api.serializers.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho.chi_tiet_phieu_xuat_kho import (  # noqa: F401,
    ChiTietPhieuXuatKhoModelSerializer,
)
from django_ledger.api.serializers.unit import (  # noqa: F401,
    EntityUnitModelSimpleSerializer,
)
from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoModel,
)


class PhieuXuatKhoSerializer(ChungTuSerializerMixin, serializers.ModelSerializer):
    """
    Serializer for PhieuXuatKhoModel.
    """

    # Use proper serializer for chi_tiet to include full details in GET requests
    chi_tiet = serializers.SerializerMethodField()
    # Reference data fields
    ma_kh_data = serializers.SerializerMethodField()
    ma_nk_data = serializers.SerializerMethodField()
    chung_tu_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()

    class Meta:
        model = PhieuXuatKhoModel
        fields = [
            'uuid',
            'entity_model',
            'nguoi_tao',
            'ngay_tao',
            'ma_ngv',
            'ma_gd',
            'ma_kh',
            'dien_giai',
            'unit_id',
            'id_progress',
            'xprogress',
            # ChungTu fields are automatically added by ChungTuSerializerMixin
            'xdatetime2',
            'ma_nt',
            'ty_gia',
            'status',
            'transfer_yn',
            't_so_luong',
            't_tien_nt',
            't_tien',
            'xfile',
            'created',
            'updated',
            'chi_tiet',
            'ma_kh_data',
            'ma_nk_data',
            'chung_tu_data',
            'ma_nt_data',
            'unit_id_data',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'created',
            'updated',
            'chi_tiet',
            'unit_id_data',
            'ma_kh_data',
            'ma_nk_data',
            'chung_tu_data',
            'ma_nt_data',
        ]

    def get_ma_kh_data(self, obj):  # noqa: C901
        """Get customer data."""
        if obj.ma_kh:
            return CustomerModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nk_data(self, obj):  # noqa: C901
        """Get document series data."""
        if obj.ma_nk:
            return QuyenChungTuListSerializer(obj.ma_nk).data
        return None

    def get_chung_tu_data(self, obj):  # noqa: C901
        """Get document data."""
        if obj.chung_tu:
            return ChungTuSerializer(obj.chung_tu).data
        return None

    def get_ma_nt_data(self, obj):  # noqa: C901
        """Get currency data."""
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """Get unit data."""
        if obj.unit_id:
            return EntityUnitModelSimpleSerializer(obj.unit_id).data
        return None

    def get_chi_tiet(self, obj):  # noqa: C901
        """Get chi tiet phieu xuat kho data."""
        try:
            # Get chi_tiet using the related_name from the model
            chi_tiet_queryset = obj.chi_tiet_phieu_xuat_kho.all()
            # Convert to list to avoid lazy evaluation issues
            chi_tiet_list = list(chi_tiet_queryset)

            if chi_tiet_list:
                return ChiTietPhieuXuatKhoModelSerializer(chi_tiet_list, many=True).data
            return []
        except Exception:
            # Return empty list on any error
            return []

    def to_internal_value(self, data):
        """
        Override to handle chi_tiet input for CREATE/UPDATE operations.
        """
        # Extract chi_tiet from input data before validation
        chi_tiet_data = data.pop('chi_tiet', []) if isinstance(data, dict) else []

        # Call parent to_internal_value
        validated_data = super().to_internal_value(data)

        # Add chi_tiet back to validated_data for service processing
        validated_data['chi_tiet'] = chi_tiet_data

        return validated_data
