"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Mua Dich Vu (Purchase Service Invoice Detail) Service.
"""

from typing import Dict, Any, Optional, Union
from uuid import UUID
from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ChiTietHoaDonMuaDichVuModel
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ChiTietHoaDonMuaDichVuRepository
from django_ledger.services.base import BaseService


class ChiTietHoaDonMuaDichVuService(BaseService):
    """
    Service class for ChiTietHoaDonMuaDichVuModel.
    Handles business logic for the purchase service invoice detail model.
    """

    def __init__(self):
        """
        Initialize the service with the repository.
        """
        self.repository = ChiTietHoaDonMuaDichVuRepository()
        super().__init__()

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[ChiTietHoaDonMuaDichVuModel]:
        """
        Retrieves a ChiTietHoaDonMuaDichVuModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to retrieve.

        Returns
        -------
        Optional[ChiTietHoaDonMuaDichVuModel]
            The ChiTietHoaDonMuaDichVuModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Lists ChiTietHoaDonMuaDichVuModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            A QuerySet of ChiTietHoaDonMuaDichVuModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def get_by_parent(self, entity_slug: str, parent_uuid: Union[str, UUID]) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Get list of child objects by parent_uuid.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        parent_uuid : Union[str, UUID]
            UUID of the parent object

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            List of child objects
        """
        return self.repository.get_by_parent(entity_slug=entity_slug, parent_uuid=parent_uuid)

    def create(self, entity_slug: str, data: Dict[str, Any]) -> ChiTietHoaDonMuaDichVuModel:
        """
        Creates a new ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new ChiTietHoaDonMuaDichVuModel.

        Returns
        -------
        ChiTietHoaDonMuaDichVuModel
            The created ChiTietHoaDonMuaDichVuModel instance.
        """
        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Create the ChiTietHoaDonMuaDichVuModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        return instance

    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[ChiTietHoaDonMuaDichVuModel]:
        """
        Updates an existing ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonMuaDichVuModel with.

        Returns
        -------
        Optional[ChiTietHoaDonMuaDichVuModel]
            The updated ChiTietHoaDonMuaDichVuModel instance, or None if not found.
        """
        # Check existence before update
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")

        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Update the ChiTietHoaDonMuaDichVuModel instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to delete.

        Returns
        -------
        bool
            True if the ChiTietHoaDonMuaDichVuModel was deleted, False otherwise.
        """
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
