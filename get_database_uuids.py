#!/usr/bin/env python3
"""
Script to extract real UUIDs from database for API testing
"""

import sqlite3
import json
from datetime import datetime

def get_database_uuids(entity_slug="tutimi-dnus2xnc"):
    """Extract real UUIDs from database for the specified entity."""

    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()

    uuids = {}

    try:
        # Get entity UUID
        cursor.execute(
            "SELECT uuid FROM django_ledger_entitymodel WHERE slug = ?",
            (entity_slug,)
        )
        entity_result = cursor.fetchone()
        if entity_result:
            uuids['entity_uuid'] = entity_result[0]
            print(f"✅ Found entity: {entity_slug} -> {entity_result[0]}")
        else:
            print(f"❌ Entity '{entity_slug}' not found")
            return None

        entity_uuid_no_dash = entity_result[0].replace('-', '')

        # First, let's check what tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        print(f"📋 Available tables: {[t[0] for t in tables[:10]]}...")

        # Get vendors (suppliers) - check actual column names
        try:
            cursor.execute("PRAGMA table_info(django_ledger_vendormodel)")
            vendor_columns = cursor.fetchall()
            print(f"🔍 Vendor table columns: {[c[1] for c in vendor_columns]}")

            cursor.execute("""
                SELECT uuid, vendor_name, vendor_number
                FROM django_ledger_vendormodel
                LIMIT 5
            """)
        except Exception as e:
            print(f"⚠️ Vendor table error: {e}")
            cursor.execute("""
                SELECT uuid, customer_name, customer_code
                FROM django_ledger_customermodel
                WHERE is_vendor = 1
                LIMIT 5
            """)
        vendors = cursor.fetchall()
        uuids['vendors'] = [{'uuid': v[0], 'name': v[1], 'number': v[2]} for v in vendors]

        # Get accounts - check actual structure
        try:
            cursor.execute("PRAGMA table_info(django_ledger_accountmodel)")
            account_columns = cursor.fetchall()
            print(f"🔍 Account table columns: {[c[1] for c in account_columns]}")

            cursor.execute("""
                SELECT uuid, code, name, role
                FROM django_ledger_accountmodel
                ORDER BY code
                LIMIT 20
            """)
        except Exception as e:
            print(f"⚠️ Account table error: {e}")
            cursor.execute("SELECT uuid, code, name FROM django_ledger_accountmodel LIMIT 20")

        accounts = cursor.fetchall()
        uuids['accounts'] = [{'uuid': a[0], 'code': a[1], 'name': a[2]} for a in accounts]

        # Get currencies
        cursor.execute("""
            SELECT uuid, code, name
            FROM django_ledger_currencymodel
            LIMIT 5
        """)
        currencies = cursor.fetchall()
        uuids['currencies'] = [{'uuid': c[0], 'code': c[1], 'name': c[2]} for c in currencies]

        # Get entity units - check structure
        try:
            cursor.execute("PRAGMA table_info(django_ledger_entityunitmodel)")
            unit_columns = cursor.fetchall()
            print(f"🔍 Unit table columns: {[c[1] for c in unit_columns]}")

            cursor.execute("""
                SELECT uuid, name, slug
                FROM django_ledger_entityunitmodel
                LIMIT 5
            """)
        except Exception as e:
            print(f"⚠️ Unit table error: {e}")
            cursor.execute("SELECT uuid, name FROM django_ledger_entityunitmodel LIMIT 5")

        units = cursor.fetchall()
        uuids['units'] = [{'uuid': u[0], 'name': u[1]} for u in units]

        # For now, let's create some sample UUIDs for testing
        # These will be placeholder UUIDs that can be replaced with real ones later
        import uuid

        uuids['sample_uuids'] = {
            'vendor_uuid': str(uuid.uuid4()),
            'staff_uuid': str(uuid.uuid4()),
            'payment_terms_uuid': str(uuid.uuid4()),
            'document_series_uuid': str(uuid.uuid4()),
            'department_uuid': str(uuid.uuid4()),
            'case_uuid': str(uuid.uuid4()),
            'contract_uuid': str(uuid.uuid4()),
            'area_uuid': str(uuid.uuid4()),
            'fee_uuid': str(uuid.uuid4()),
            'cost_center_uuid': str(uuid.uuid4())
        }

    except Exception as e:
        print(f"❌ Error querying database: {e}")
        return None
    finally:
        conn.close()

    return uuids

def print_summary(uuids):
    """Print a summary of found UUIDs."""
    if not uuids:
        print("❌ No UUIDs found")
        return

    print("\n" + "="*80)
    print("📋 DATABASE UUID SUMMARY")
    print("="*80)

    for category, items in uuids.items():
        if category == 'entity_uuid':
            print(f"🏢 Entity UUID: {items}")
        elif isinstance(items, list):
            print(f"\n📂 {category.upper()}: {len(items)} items")
            for item in items[:3]:  # Show first 3 items
                if isinstance(item, dict):
                    name = item.get('name', item.get('code', 'N/A'))
                    print(f"   • {item['uuid']} - {name}")
            if len(items) > 3:
                print(f"   ... and {len(items) - 3} more")

if __name__ == "__main__":
    print("🔍 Extracting UUIDs from database...")
    uuids = get_database_uuids("tutimi-dnus2xnc")

    if uuids:
        print_summary(uuids)

        # Save to JSON file for reference
        with open('database_uuids.json', 'w', encoding='utf-8') as f:
            json.dump(uuids, f, indent=2, ensure_ascii=False)
        print(f"\n💾 UUIDs saved to database_uuids.json")
    else:
        print("❌ Failed to extract UUIDs")
