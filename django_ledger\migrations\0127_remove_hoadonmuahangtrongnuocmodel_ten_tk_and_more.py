# Generated by Django 4.2.10 on 2025-07-02 04:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            'django_ledger',
            '0126_remove_thuephieunhapchiphimuahangmodel_thue_suat_and_more',
        ),
    ]

    operations = [
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='he_so',
            field=models.DecimalField(
                decimal_places=2,
                default=1,
                help_text='Conversion ratio',
                max_digits=18,
                verbose_name='<PERSON>ệ số',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='id_nhap',
            field=models.IntegerField(
                default=0, help_text='Import ID', verbose_name='ID nhập'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='id_px',
            field=models.IntegerField(
                default=0, help_text='Export ID', verbose_name='ID phiếu xuất'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='id_yc',
            field=models.IntegerField(
                default=0, help_text='Request ID', verbose_name='ID yêu cầu'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='line_nhap',
            field=models.IntegerField(
                default=0, help_text='Import line', verbose_name='Dòng nhập'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='line_px',
            field=models.IntegerField(
                default=0, help_text='Export line', verbose_name='Dòng phiếu xuất'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='line_yc',
            field=models.IntegerField(
                default=0, help_text='Request line', verbose_name='Dòng yêu cầu'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='lo_yn',
            field=models.IntegerField(
                default=0, help_text='Batch Y/N', verbose_name='Lô Y/N'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='ma_lsx',
            field=models.CharField(
                default='',
                help_text='Production order code',
                max_length=50,
                verbose_name='Mã lệnh sản xuất',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='qc_yn',
            field=models.IntegerField(
                default=0, help_text='QC Y/N', verbose_name='QC Y/N'
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='sl_px',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text='Export quantity',
                max_digits=18,
                verbose_name='Số lượng phiếu xuất',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='ten_kho',
            field=models.CharField(
                default='',
                help_text='Warehouse name',
                max_length=100,
                verbose_name='Tên kho',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='ten_lo',
            field=models.CharField(
                default='',
                help_text='Batch name',
                max_length=100,
                verbose_name='Tên lô',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='ten_vi_tri',
            field=models.CharField(
                default='',
                help_text='Location name',
                max_length=100,
                verbose_name='Tên vị trí',
            ),
        ),
        migrations.AlterField(
            model_name='chitietphieuxuatkhomodel',
            name='vi_tri_yn',
            field=models.IntegerField(
                default=0, help_text='Location Y/N', verbose_name='Vị trí Y/N'
            ),
        ),
    ]
