#!/usr/bin/env python3
"""
Simple test for HoaDonMuaDichVu API without nested data
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://127.0.0.1:8003"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "tutimi"

# Create basic auth header
auth_string = f"{USERNAME}:{PASSWORD}"
auth_bytes = auth_string.encode('ascii')
auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

headers = {
    'Authorization': f'Basic {auth_b64}',
    'Content-Type': 'application/json'
}

def test_simple_create():
    """Test creating a simple hoa don without nested data"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"
    
    data = {
        "ma_gd": "GD001",
        "pc_tao_yn": "N",
        "ma_httt": "TM",
        "loai_ck": "PT",
        "ck_tl_nt": 0.0,
        "ma_so_thue": "0123456789",
        "du_cn_thu": 0.0,
        "dia_chi": "123 Main Street",
        "ong_ba": "Nguyen Van A",
        "e_mail": "<EMAIL>",
        "tk": "3314d5f7-4cb9-4253-8833-525dcd23801f",
        "dien_giai": "Simple test without nested data",
        "id": "ID001",
        "unit_id": "7043926df26f463299c344f2c13ac6e4",
        "ma_nk": "c1a10a51-9d62-4b6a-8c62-76432af3ca03",
        "so_ct": "MDV1.01.25.000017",
        "i_so_ct": 17,
        "ngay_ct": "2025-01-02",
        "ngay_lct": "2025-01-02",
        "so_ct0": "HD001",
        "so_ct2": "HD001-2",
        "ngay_ct0": "2025-01-02",
        "ty_gia": 1.0,
        "status": "1",
        "transfer_yn": "N",
        "ma_ngv": "NGV001",
        "pc_ngay_ct": "2025-01-02",
        "pc_ma_ct": "PC001",
        "pc_ten_ct": "Phieu chi test",
        "pc_ma_nk": "PC",
        "pc_ten_nk": "Phieu chi",
        "pc_tknh": "1111",
        "pc_tk": "1112",
        "pc_ten_tk": "Tai khoan ngan hang",
        "pc_t_tt_nt": 1000000.0,
        "so_ct_tt": "TT001",
        "t_thue_nt": 100000.0,
        "t_thue": 100000.0,
        "t_tien_nt": 1000000.0,
        "t_tien": 1000000.0,
        "t_ck_nt_ex": 0.0,
        "t_ck_ex": 0.0,
        "t_ck_nt": 0.0,
        "t_ck": 0.0,
        "t_tt_nt": 1100000.0,
        "t_tt": 1100000.0
        # NO chi_tiet and thue
    }
    
    print("Testing SIMPLE CREATE HoaDonMuaDichVu...")
    print(f"URL: {url}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            result = response.json()
            print("✅ SIMPLE CREATE test PASSED")
            print(f"UUID: {result.get('uuid')}")
            print(f"chi_tiet_data: {result.get('chi_tiet_data', 'NOT_FOUND')}")
            print(f"thue_data: {result.get('thue_data', 'NOT_FOUND')}")
            return result
        else:
            print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            print("❌ SIMPLE CREATE test FAILED")
            return None
            
    except Exception as e:
        print(f"❌ SIMPLE CREATE test ERROR: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Starting Simple HoaDonMuaDichVu API Test...")
    result = test_simple_create()
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
