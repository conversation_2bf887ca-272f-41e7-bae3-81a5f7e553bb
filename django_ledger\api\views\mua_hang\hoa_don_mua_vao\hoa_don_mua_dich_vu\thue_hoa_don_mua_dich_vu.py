"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Thue Hoa Don <PERSON> (Purchase Service Invoice Tax) ViewSet.
"""

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter

from django_ledger.api.decorators import api_exception_handler
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ThueHoaDonMuaDichVuModel
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ThueHoaDonMuaDichVuSerializer
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ThueHoaDonMuaDichVuService
from django_ledger.api.views.common import ERPPagination


class ThueHoaDonMuaDichVuViewSet(viewsets.ViewSet):
    """
    ViewSet for ThueHoaDonMuaDichVuModel.
    """
    permission_classes = [IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = ThueHoaDonMuaDichVuService()

    def get_serializer_context(self):
        """
        Returns the serializer context.
        """
        return {
            'request': self.request,
            'entity_slug': self.kwargs['entity_slug']
        }

    def get_serializer(self, *args, **kwargs):
        """
        Returns the serializer instance.
        """
        kwargs['context'] = self.get_serializer_context()
        return ThueHoaDonMuaDichVuSerializer(*args, **kwargs)

    @api_exception_handler
    def list(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None):
        """
        Lists ThueHoaDonMuaDichVuModel instances for a specific invoice.
        """
        queryset = self.service.get_by_parent(entity_slug=entity_slug, parent_uuid=hoa_don_mua_dich_vu_uuid)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @api_exception_handler
    def retrieve(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None, pk=None):
        """
        Retrieves a ThueHoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'ThueHoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @api_exception_handler
    def create(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None):
        """
        Creates a new ThueHoaDonMuaDichVuModel instance.
        """
        serializer = self.get_serializer(data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data
            user_model = request.user

            # Set parent reference
            validated_data['hoa_don'] = hoa_don_mua_dich_vu_uuid

            # Create the ThueHoaDonMuaDichVuModel instance using service
            instance = self.service.create(
                entity_slug=entity_slug,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=instance.uuid)
            serializer = self.get_serializer(instance)

            return Response(serializer.data, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def update(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None, pk=None):
        """
        Updates an existing ThueHoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'ThueHoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data

            # Update the ThueHoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
            serializer = self.get_serializer(instance)

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def partial_update(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None, pk=None):
        """
        Partially updates an existing ThueHoaDonMuaDichVuModel instance.
        """
        instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)

        if not instance:
            return Response(
                {'detail': 'ThueHoaDonMuaDichVu not found.'},
                status=status.HTTP_404_NOT_FOUND
            )

        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if serializer.is_valid():
            # Extract data from serializer
            validated_data = serializer.validated_data

            # Update the ThueHoaDonMuaDichVuModel instance using service
            instance = self.service.update(
                entity_slug=entity_slug,
                uuid=pk,
                data=validated_data
            )

            # Refresh instance to get all related data
            instance = self.service.get_by_id(entity_slug=entity_slug, uuid=pk)
            serializer = self.get_serializer(instance)

            return Response(serializer.data)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @api_exception_handler
    def destroy(self, request, entity_slug=None, hoa_don_mua_dich_vu_uuid=None, pk=None):
        """
        Deletes a ThueHoaDonMuaDichVuModel instance.
        """
        success = self.service.delete(entity_slug=entity_slug, uuid=pk)

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(
            {'detail': 'ThueHoaDonMuaDichVu not found.'},
            status=status.HTTP_404_NOT_FOUND
        )
