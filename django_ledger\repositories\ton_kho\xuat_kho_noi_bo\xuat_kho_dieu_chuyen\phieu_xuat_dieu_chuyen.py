"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatDieuChuyen (Warehouse Transfer) repository implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models.chung_tu.chung_tu_item import ChungTuItemModel  # noqa: F401
from django_ledger.models.entity import EntityModel  # noqa: F401,
from django_ledger.models.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen import (  # noqa: F401,
    PhieuXuatDieuChuyenModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils import (
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuXuatDieuChuyenRepository(BaseRepository):
    """
    Repository class for PhieuXuatDieuChuyenModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=PhieuXuatDieuChuyenModel)

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuXuatDieuChuyenModel]:  # noqa: C901
        """
        Retrieves a PhieuXuatDieuChuyenModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel to retrieve.

        Returns
        -------
        Optional[PhieuXuatDieuChuyenModel]
            The PhieuXuatDieuChuyenModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.for_entity(
                entity_slug=entity_slug
            ).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Lists all PhieuXuatDieuChuyenModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuXuatDieuChuyenModel instances.
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuXuatDieuChuyenModel:  # noqa: C901
        """
        Creates a new PhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuXuatDieuChuyenModel.

        Returns
        -------
        PhieuXuatDieuChuyenModel
            The created PhieuXuatDieuChuyenModel instance.
        """
        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Use utility function to extract and process ChungTu fields
        chung_tu_fields, data_copy = process_chung_tu_fields_extraction_and_conversion(
            data
        )

        # Convert UUID strings to model instances for remaining data
        processed_data = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()
        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuXuatDieuChuyenModel]:  # noqa: C901
        """
        Updates an existing PhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel to update.
        data : Dict[str, Any]
            The data to update the PhieuXuatDieuChuyenModel with.

        Returns
        -------
        Optional[PhieuXuatDieuChuyenModel]
            The updated PhieuXuatDieuChuyenModel instance, or None if not found.
        """
        instance = self.model_class.objects.filter(entity_model__slug=entity_slug).get(
            uuid=uuid
        )

        # Use utility function to handle ChungTu field updates
        return update_instance_with_chung_tu_fields(
            instance=instance,
            data=data,
            convert_uuids_func=self.convert_uuids_to_model_instances,
        )

    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes a PhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return False

        instance.delete()
        return True

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        PhieuXuatDieuChuyen related fields and other foreign keys.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'ma_ngv',
            'ma_gd',
            'dien_giai',
            'so_buoc',
            'status',
            'ma_tthddt',
            'so_ct_hddt',
            'lenh_dd',
            'so_ct2_hddt',
            'ma_mau_ct_hddt',
            'nguoi_dd',
            'viec_dd',
            'so_hd_vc',
            'ly_do_huy',
            'ly_do',
            'ten_kh_vc',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        return data_copy


