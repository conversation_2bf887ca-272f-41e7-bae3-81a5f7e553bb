"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) service implementation.  # noqa: E501
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import (  # noqa: F401,
    KhaiBaoTamDungKhauHaoTSCDModel,
)
from django_ledger.repositories.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import (  # noqa: F401,
    KhaiBaoTamDungKhauHaoTSCDRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,


class KhaiBaoTamDungKhauHaoTSCDService(BaseService):
    """
    Service class for KhaiBaoTamDungKhauHaoTSCDModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = KhaiBaoTamDungKhauHaoTSCDRepository()
        super().__init__()

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[KhaiBaoTamDungKhauHaoTSCDModel]:  # noqa: C901
        """
        Retrieves a KhaiBaoTamDungKhauHaoTSCDModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to retrieve.

        Returns
        -------
        Optional[KhaiBaoTamDungKhauHaoTSCDModel]
            The KhaiBaoTamDungKhauHaoTSCDModel with the given UUID, or None if not found.  # noqa: E501
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists KhaiBaoTamDungKhauHaoTSCDModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of KhaiBaoTamDungKhauHaoTSCDModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoTamDungKhauHaoTSCDModel:  # noqa: C901
        """
        Creates a new KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoTamDungKhauHaoTSCDModel.

        Returns
        -------
        KhaiBaoTamDungKhauHaoTSCDModel
            The created KhaiBaoTamDungKhauHaoTSCDModel instance.
        """

        # Create the KhaiBaoTamDungKhauHaoTSCDModel instance
        return self.repository.create(entity_slug=entity_slug, data=data)

    def update(  # noqa: C901
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoTamDungKhauHaoTSCDModel]:
        """
        Updates an existing KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to update.
        data : Dict[str, Any]
            The data to update the KhaiBaoTamDungKhauHaoTSCDModel with.

        Returns
        -------
        Optional[KhaiBaoTamDungKhauHaoTSCDModel]
            The updated KhaiBaoTamDungKhauHaoTSCDModel instance, or None if not found.
        """
        # Check if the record exists
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")


        # Update the KhaiBaoTamDungKhauHaoTSCDModel instance
        return self.repository.update(
            entity_slug=entity_slug, uuid=uuid, data=data
        )

    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes a KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to delete.

        Returns
        -------
        bool
            True if the KhaiBaoTamDungKhauHaoTSCDModel was deleted, False otherwise.
        """
        # Check if the record exists
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")

        # Delete the KhaiBaoTamDungKhauHaoTSCDModel instance
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)
