"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Dich Vu (Purchase Service Invoice) Repository.
"""

from typing import Dict, Any, Optional, Union
from uuid import UUID
from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
from django_ledger.models import EntityModel
from django_ledger.repositories._utils.chung_tu_item_utils import (
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository


class HoaDonMuaDichVuRepository(BaseRepository[HoaDonMuaDichVuModel]):
    """
    Repository class for HoaDonMuaDichVuModel.
    Handles database operations for the purchase service invoice model.
    """

    def __init__(self):
        """
        Initialize the repository with the HoaDonMuaDichVuModel.
        """
        super().__init__(model_class=HoaDonMuaDichVuModel)

    def get_queryset(self) -> QuerySet[HoaDonMuaDichVuModel]:
        """
        Returns the base queryset for HoaDonMuaDichVuModel.

        Returns
        -------
        QuerySet[HoaDonMuaDichVuModel]
            The base queryset for HoaDonMuaDichVuModel.
        """
        return self.model_class.objects.all()

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[HoaDonMuaDichVuModel]:
        """
        Retrieves a HoaDonMuaDichVuModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to retrieve.

        Returns
        -------
        Optional[HoaDonMuaDichVuModel]
            The HoaDonMuaDichVuModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.for_entity(entity_slug=entity_slug).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet[HoaDonMuaDichVuModel]:
        """
        Lists HoaDonMuaDichVuModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet[HoaDonMuaDichVuModel]
            A QuerySet of HoaDonMuaDichVuModel instances.
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug).filter(**kwargs)

    def create(self, entity_slug: str, data: Dict[str, Any]) -> HoaDonMuaDichVuModel:
        """
        Creates a new HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new HoaDonMuaDichVuModel.

        Returns
        -------
        HoaDonMuaDichVuModel
            The created HoaDonMuaDichVuModel instance.
        """
        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Use utility function to extract and process ChungTu fields
        chung_tu_fields, data_copy = process_chung_tu_fields_extraction_and_conversion(
            data
        )

        # Convert UUID strings to model instances for remaining data (including chung_tu)
        processed_data_copy = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data_copy)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()

        return instance

    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[HoaDonMuaDichVuModel]:
        """
        Updates an existing HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaDichVuModel with.

        Returns
        -------
        Optional[HoaDonMuaDichVuModel]
            The updated HoaDonMuaDichVuModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not instance:
            return None

        # Use utility function to handle ChungTu field updates
        return update_instance_with_chung_tu_fields(
            instance=instance,
            data=data,
            convert_uuids_func=self.convert_uuids_to_model_instances,
        )

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to delete.

        Returns
        -------
        bool
            True if the HoaDonMuaDichVuModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False


