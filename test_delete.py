#!/usr/bin/env python3
"""
Test DELETE API
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://127.0.0.1:8003"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "tutimi"

# Create basic auth header
auth_string = f"{USERNAME}:{PASSWORD}"
auth_bytes = auth_string.encode('ascii')
auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

headers = {
    'Authorization': f'Basic {auth_b64}',
    'Content-Type': 'application/json'
}

def test_delete():
    """Test DELETE API"""
    # Use the UUID from the last successful create
    uuid = "98db46fc-1742-495e-b140-e8e0f162c133"
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{uuid}/"
    
    print("Testing DELETE HoaDonMuaDichVu...")
    print(f"URL: {url}")
    
    try:
        response = requests.delete(url, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 204:
            print("✅ DELETE test PASSED")
            return True
        else:
            try:
                result = response.json()
                print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except:
                print(f"Response: {response.text}")
            print("❌ DELETE test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ DELETE test ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting DELETE Test...")
    result = test_delete()
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
