# 🚀 Expert ERP Report API Template (Agent-Optimized)

## 🎯 **AGENT DECISION TREE**

**Choose implementation path based on complexity:**

- 🚀 **Simple Report** (5 min): Basic fields, standard filters → **Section A**
- 🔧 **Standard Report** (15 min): Multiple tables, business logic → **Section B**
- 🔍 **Complex Report** (30 min): Advanced calculations, custom logic → **Section C**

---

## 📋 **USER INPUT TEMPLATE**

````markdown
I want to create ERP report API: **"[EXACT_REPORT_NAME]"**

**1. cURL Request:**

```bash
[PASTE_COMPLETE_CURL_COMMAND_HERE]
```
````

**2. Response Fields:**

```json
["stt", "field1", "field2", "field3", "field4"]
```

**3. Module Structure:**

- Module: [MODULE_NAME] (e.g., ton_kho, ban_hang, tai_san)
- Sub-module: [SUB_MODULE_NAME] (e.g., bao_cao_ton_kho, cong_no_khach_hang)
- Report name: [REPORT_NAME] (e.g., bang_ke_ton_kho, bang_can_doi_phat_sinh)

**4. Complexity Level:** [Simple/Standard/Complex]

**5. Expected Endpoint:**
`/api/entities/{entity_slug}/erp/[module]/[sub-module]/[report-name]/`

````

---

# 🚀 **SECTION A: SIMPLE REPORT (5 minutes)**

## ⚡ **Quick Implementation for Basic Reports**

**Use when:** Basic fields, standard filters, no complex business logic

### **Step 1: Copy Working Pattern**
```bash
# Copy proven pattern
cp -r django_ledger/services/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra \
      django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME]

# Copy API structure
mkdir -p django_ledger/api/{serializers,views}/[MODULE]/[SUB_MODULE]/[REPORT_NAME]
cp django_ledger/api/serializers/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/* \
   django_ledger/api/serializers/[MODULE]/[SUB_MODULE]/[REPORT_NAME]/
cp django_ledger/api/views/thue/bang_ke_thue/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/* \
   django_ledger/api/views/[MODULE]/[SUB_MODULE]/[REPORT_NAME]/
````

### **Step 2: Replace Names**

```bash
# Replace class names in all files
find django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME] -name "*.py" -exec sed -i '' \
  -e 's/BangKeHoaDonChungTuHangHoaDichVuBanRa/[YourClassName]/g' \
  -e 's/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/[your_report_name]/g' {} \;

find django_ledger/api -path "*[MODULE]/[SUB_MODULE]/[REPORT_NAME]*" -name "*.py" -exec sed -i '' \
  -e 's/BangKeHoaDonChungTuHangHoaDichVuBanRa/[YourClassName]/g' \
  -e 's/bang_ke_hoa_don_chung_tu_hang_hoa_dich_vu_ban_ra/[your_report_name]/g' {} \;
```

### **Step 3: Update Response Fields**

In `utils/data_transformers.py`:

```python
def transform_report_item(item: Dict[str, Any], index: int) -> Dict[str, Any]:
    return {
        'stt': index,
        'field1': item.get('field1', ''),  # Replace with user's fields
        'field2': item.get('field2', ''),
        'field3': item.get('field3', ''),
        # Add ALL fields from user specification
    }
```

### **Step 4: Test**

```bash
cd django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME]
python create_[report_name]_test_data.py my-new-company-xyz
curl -X POST 'http://localhost:8003/api/entities/my-new-company-xyz/erp/[module]/[sub-module]/[report-name]/' \
  -u 'admin:minhdang123' -H 'Content-Type: application/json' -d '[USER_CURL_DATA]'
```

**✅ Success Criteria:** API returns 200 with count > 0, all fields present

---

# 🔧 **SECTION B: STANDARD REPORT (15 minutes)**

## 📊 **Comprehensive Implementation for Standard Reports**

**Use when:** Multiple tables, business logic, complex filters

### **Phase 1: Analysis (3 minutes)**

```bash
# 1. Find working pattern
find django_ledger/services -name "*bang_ke*" -type d | head -3

# 2. Check entity
python manage.py shell -c "
from django_ledger.models import EntityModel
entity = EntityModel.objects.filter(slug__icontains='company').first()
print(f'Entity: {entity.slug if entity else \"Not found\"}')"

# 3. Check relevant tables (replace [keyword] with table hint from cURL)
python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\" AND name LIKE \"%[keyword]%\"')
print('Tables:', [row[0] for row in cursor.fetchall()])"
```

### **Phase 2: Implementation (8 minutes)**

```bash
# Copy appropriate pattern based on complexity
cp -r django_ledger/services/ban_hang/cong_no_khach_hang/bang_can_doi_phat_sinh_cong_no \
      django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME]

# Copy API structure
mkdir -p django_ledger/api/{serializers,views}/[MODULE]/[SUB_MODULE]/[REPORT_NAME]
cp django_ledger/api/serializers/ban_hang/cong_no_khach_hang/bang_can_doi_phat_sinh_cong_no/* \
   django_ledger/api/serializers/[MODULE]/[SUB_MODULE]/[REPORT_NAME]/
```

### **Phase 3: Adaptation (4 minutes)**

**3.1 Update SQL Query** in `utils/sql_queries.py`:

```python
MAIN_REPORT_QUERY = """
    SELECT
        main.[field1] as [response_field1],
        main.[field2] as [response_field2],
        detail.[field3] as [response_field3]
    FROM [YOUR_MAIN_TABLE] main
    LEFT JOIN [YOUR_DETAIL_TABLE] detail ON main.uuid = detail.[main_table]_id
    WHERE {conditions}
    ORDER BY main.[date_field]
"""
```

**3.2 Update Response Fields** in `utils/data_transformers.py`:

```python
def transform_report_item(item: Dict[str, Any], index: int) -> Dict[str, Any]:
    return {
        'stt': index,
        '[response_field1]': item.get('[response_field1]', ''),
        '[response_field2]': float(item.get('[response_field2]', 0)),
        '[response_field3]': item.get('[response_field3]', ''),
        # Map ALL response fields from user specification
    }
```

**✅ Success Criteria:** API returns 200, all fields present, response time < 2 seconds

---

# 🔍 **SECTION C: COMPLEX REPORT (30 minutes)**

## 🎯 **Advanced Implementation for Complex Reports**

**Use when:** Advanced calculations, custom business logic, multiple data sources

### **Phase 1: Deep Analysis (10 minutes)**

```bash
# 1. Database schema analysis
python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute('PRAGMA table_info([MAIN_TABLE])')
print('Main table structure:')
for row in cursor.fetchall(): print(f'  {row}')

cursor.execute('PRAGMA foreign_key_list([MAIN_TABLE])')
print('Foreign keys:', cursor.fetchall())
"

# 2. Business logic analysis
# - Identify calculation requirements
# - Map complex filter relationships
# - Plan data aggregation strategy

# 3. Pattern selection
find django_ledger/services -name "*[similar_pattern]*" -type d
```

### **Phase 2: Advanced Implementation (15 minutes)**

- Copy most similar complex pattern
- Implement custom business logic
- Create advanced SQL queries with CTEs
- Handle complex parameter parsing
- Implement data validation

### **Phase 3: Testing & Optimization (5 minutes)**

- Create comprehensive test data
- Test all parameter combinations
- Validate business logic compliance
- Performance optimization
- Error handling verification

**✅ Success Criteria:** All business rules implemented, performance optimized, comprehensive testing

---

## 🎯 **CORE PATTERNS (ESSENTIAL)**

### **1. Service Pattern (Simplified & Robust)**

```python
class [ReportName]Service(BaseService):
    """Service class for [ReportName] business logic."""

    def generate_report(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Main orchestration method."""
        try:
            # Get filtered data using utils
            data = self._get_filtered_data(entity_slug, filters)
            # Process and format using utils
            return self._process_report_data(data)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating report: {str(e)}", exc_info=True)
            return []

    def _get_filtered_data(self, entity_slug: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get filtered data using direct SQL with utils."""
        from django.db import connection
        from django_ledger.models import EntityModel
        from .utils.sql_queries import DebtBalanceSQLQueries

        # Get entity UUID - always filter by entity first
        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            entity_uuid = str(entity.uuid)
        except EntityModel.DoesNotExist:
            return []

        # Build complete query using utils
        sql_query = DebtBalanceSQLQueries.build_customer_debt_balance_query(filters)

        # Execute query with proper error handling
        with connection.cursor() as cursor:
            cursor.execute(sql_query, {'entity_uuid': entity_uuid, 'start_date': filters.get('start_date'), 'end_date': filters.get('end_date')})
            columns = [col[0] for col in cursor.description]
            results = []

            for row in cursor.fetchall():
                row_dict = dict(zip(columns, row))
                results.append(row_dict)

            return results

    def _process_report_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process data using utils transformers."""
        from .utils.data_transformers import transform_report_data
        return transform_report_data(data)
```

### **2. Enhanced Utils Pattern (3-File Structure)**

```python
# utils/__init__.py - Clean exports with error handling
try:
    from .sql_queries import (
        ReportSQLQueries,  # Use class-based approach
        FIELD_MAP,
    )
    from .test_data_utils import (
        ReportTestDataUtils,  # Comprehensive test data utilities
    )
    from .data_transformers import (
        transform_report_item,
        transform_report_data,
        validate_response_data,  # NEW: Response validation
    )
except ImportError as e:
    # Handle import errors gracefully
    print(f"Warning: Utils import error: {e}")
    pass

# utils/sql_queries.py - Class-based SQL utilities
class ReportSQLQueries:
    @staticmethod
    def build_main_query(filters: Dict[str, Any]) -> str:
        """Build main SQL query with dynamic conditions."""
        return """
            SELECT
                ROW_NUMBER() OVER (ORDER BY main.id) as stt,
                main.field1,
                main.field2,
                main.field3
            FROM [YOUR_MAIN_TABLE] main
            WHERE main.entity_model_id = %(entity_uuid)s
              AND main.date_field BETWEEN %(start_date)s AND %(end_date)s
            ORDER BY main.date_field DESC
        """

# utils/test_data_utils.py - Test data utilities
class ReportTestDataUtils:
    @staticmethod
    def get_test_data_from_script(entity_uuid: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get real test data from script."""
        # Implementation similar to debt balance report
        pass

# utils/data_transformers.py - Data transformation
def transform_report_item(item: Dict[str, Any], index: int) -> Dict[str, Any]:
    """Transform single item to API response format."""
    return {
        'stt': index,
        'field1': str(item.get('field1', '')),
        'field2': float(item.get('field2', 0)),
        'field3': str(item.get('field3', '')),
        # Map ALL serializer fields with proper types
    }

def transform_report_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Transform list of data to API response format."""
    return [
        transform_report_item(item, index)
        for index, item in enumerate(data, 1)
    ]
```

### **3. Serializer Pattern**

```python
class [ReportName]RequestSerializer(serializers.Serializer):
    # Core filters with comprehensive validation
    ngay_ct1 = serializers.DateField(required=True)
    ngay_ct2 = serializers.DateField(required=True)

    # UUID filters with CharField flexibility
    ma_field = serializers.CharField(required=False, allow_blank=True, max_length=50)

    # Standard parameters
    ma_unit = serializers.UUIDField(required=False, allow_null=True)
    mau_bc = serializers.IntegerField(required=False, default=20)

    # Test parameter for development
    test = serializers.CharField(required=False, allow_blank=True, max_length=10)

    def validate(self, data):
        # Business rule validation
        if data['ngay_ct1'] > data['ngay_ct2']:
            raise serializers.ValidationError("Start date must be before end date")
        return data

class [ReportName]ResponseSerializer(serializers.Serializer):
    stt = serializers.IntegerField()
    field1 = serializers.CharField(max_length=50)
    field2 = serializers.DecimalField(max_digits=15, decimal_places=3)
    field3 = serializers.CharField(max_length=100)
    # Add all response fields with proper types
```

### **4. ViewSet Pattern**

```python
@extend_schema_view(...)
class [ReportName]ViewSet(viewsets.ViewSet):
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = [ReportName]Service()

    def get_report(self, request, entity_slug):
        """Generate report with validation, processing, and pagination."""
        # Validate request
        serializer = [ReportName]RequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=400)

        # Generate report
        report_data = self.service.generate_report(entity_slug, serializer.validated_data)

        # Paginate and serialize response
        paginated_data = self.pagination_class().paginate_queryset(report_data, request)
        response_serializer = [ReportName]ResponseSerializer(paginated_data, many=True)
        return self.pagination_class().get_paginated_response(response_serializer.data)
```

---

## 🚨 **TROUBLESHOOTING GUIDE**

### **Issue 1: API Returns 0 Records**

```bash
# Quick diagnosis
python manage.py shell -c "
from django_ledger.models import EntityModel
entity = EntityModel.objects.filter(slug__icontains='company').first()
print(f'Entity: {entity.slug if entity else \"Not found\"}')
if entity:
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute('SELECT COUNT(*) FROM [YOUR_MAIN_TABLE] WHERE entity_model_id = ?', [str(entity.uuid)])
    print(f'Records for entity: {cursor.fetchone()[0]}')
"
```

### **Issue 2: Import Errors**

```bash
# Fix missing __init__.py files
touch django_ledger/services/[MODULE]/__init__.py
touch django_ledger/services/[MODULE]/[SUB_MODULE]/__init__.py
touch django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME]/__init__.py
touch django_ledger/services/[MODULE]/[SUB_MODULE]/[REPORT_NAME]/utils/__init__.py
```

### **Issue 3: Database Errors**

```bash
# Check table structure
python manage.py shell -c "
from django.db import connection
cursor = connection.cursor()
cursor.execute('PRAGMA table_info([YOUR_MAIN_TABLE])')
print('Table structure:')
for row in cursor.fetchall(): print(f'  {row}')
"
```

---

## ✅ **SUCCESS CRITERIA (4 ITEMS ONLY)**

- [ ] ✅ **API returns 200** with count > 0
- [ ] ✅ **All response fields present** (match user specification)
- [ ] ✅ **Test data script works** (no errors)
- [ ] ✅ **Response time < 3 seconds**

---

## 📋 **PLACEHOLDER REFERENCE**

**Replace these in ALL files:**

| Placeholder         | Example                          | Source                    |
| ------------------- | -------------------------------- | ------------------------- |
| `[MODULE]`          | `ban_hang`                       | User input                |
| `[SUB_MODULE]`      | `cong_no_khach_hang`             | User input                |
| `[REPORT_NAME]`     | `bang_can_doi_phat_sinh_cong_no` | User input                |
| `[YourClassName]`   | `BangCanDoiPhatSinhCongNo`       | CamelCase of report name  |
| `[YOUR_MAIN_TABLE]` | `django_ledger_customermodel`    | From cURL analysis        |
| `[response_field1]` | `ma_kh`                          | From user response fields |

---

## 💡 **CONCRETE EXAMPLE**

````markdown
I want to create ERP report API: **"Báo Cáo Tồn Kho"**

**1. cURL Request:**

```bash
curl 'http://localhost:8003/api/entities/my-company/erp/ton-kho/bao-cao-ton-kho/bao-cao-ton-kho/' \
  -H 'Content-Type: application/json' \
  -u 'admin:password' \
  --data-raw '{
    "ngay_ct1": "2024-01-01",
    "ngay_ct2": "2024-12-31",
    "ma_kho": "uuid-kho-1"
  }'
```
````

**2. Response Fields:**

```json
["stt", "ma_kho", "ten_kho", "sl_ton", "gia_tri_ton"]
```

**3. Module Structure:**

- Module: ton_kho
- Sub-module: bao_cao_ton_kho
- Report name: bao_cao_ton_kho

**4. Complexity Level:** Simple

**Agent executes Section A (5 minutes):**

1. Copy pattern from bang_ke_hoa_don
2. Replace BangKeHoaDonChungTuHangHoaDichVuBanRa → BaoCaoTonKho
3. Update response fields: stt, ma_kho, ten_kho, sl_ton, gia_tri_ton
4. Test API

**Result: Working API in 5 minutes** ✅

```

---

## 🎯 **AGENT NOTES**

1. **Always use concrete examples** when placeholders are unclear
2. **Test each step immediately** - don't wait until the end
3. **Use fallback values** when user input is incomplete
4. **Copy working patterns exactly** - don't reinvent
5. **Keep it simple** - avoid over-engineering

**Total time: 5-30 minutes depending on complexity level**
```
