"""
Sales Return Summary Report URLs (Feature Level).

This module defines URL patterns for the sales return summary report feature.
"""

from django.urls import path

from django_ledger.api.views.ban_hang.bao_cao_ban_hang.bao_cao_tong_hop_hang_ban_tra_lai import (
    BaoCaoTongHopHangBanTraLaiViewSet
)

app_name = 'bao_cao_tong_hop_hang_ban_tra_lai'

# URL patterns - Single endpoint for sales return summary report with filters as POST body data
urlpatterns = [
    # Sales Return Summary Report endpoint - returns report directly with filter POST body data
    path(
        "",
        BaoCaoTongHopHangBanTraLaiViewSet.as_view({"post": "get_report"}),
        name="bao-cao-tong-hop-hang-ban-tra-lai-report",
    ),
]

# Final URL will be: /api/ban_hang/bao-cao-ban-hang/bao-cao-tong-hop-hang-ban-tra-lai/
