#!/usr/bin/env python3
"""
Simple script to extract basic UUIDs from database
"""

import sqlite3
import uuid

def get_basic_uuids():
    """Get basic UUIDs from database."""
    
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    uuids = {}
    
    try:
        # Get entity UUID
        cursor.execute("SELECT uuid FROM django_ledger_entitymodel WHERE slug = 'tutimi-dnus2xnc'")
        entity_result = cursor.fetchone()
        if entity_result:
            uuids['entity_uuid'] = entity_result[0]
            print(f"✅ Entity UUID: {entity_result[0]}")
        
        # Get some vendors
        cursor.execute("SELECT uuid, vendor_name FROM django_ledger_vendormodel LIMIT 3")
        vendors = cursor.fetchall()
        if vendors:
            uuids['vendor_uuid'] = vendors[0][0]
            print(f"✅ Vendor UUID: {vendors[0][0]} ({vendors[0][1]})")
        
        # Get some accounts
        cursor.execute("SELECT uuid, code, name FROM django_ledger_accountmodel WHERE code IS NOT NULL ORDER BY code LIMIT 10")
        accounts = cursor.fetchall()
        if accounts:
            # Find specific account types
            for acc in accounts:
                if acc[1] and acc[1].startswith('1'):  # Asset account
                    uuids['asset_account_uuid'] = acc[0]
                    print(f"✅ Asset Account: {acc[0]} ({acc[1]} - {acc[2]})")
                    break
            
            for acc in accounts:
                if acc[1] and acc[1].startswith('2'):  # Liability account
                    uuids['liability_account_uuid'] = acc[0]
                    print(f"✅ Liability Account: {acc[0]} ({acc[1]} - {acc[2]})")
                    break
                    
            for acc in accounts:
                if acc[1] and acc[1].startswith('6'):  # Expense account
                    uuids['expense_account_uuid'] = acc[0]
                    print(f"✅ Expense Account: {acc[0]} ({acc[1]} - {acc[2]})")
                    break
        
        # Get entity units
        cursor.execute("SELECT uuid, name FROM django_ledger_entityunitmodel LIMIT 3")
        units = cursor.fetchall()
        if units:
            uuids['unit_uuid'] = units[0][0]
            print(f"✅ Unit UUID: {units[0][0]} ({units[0][1]})")
        
        # Generate sample UUIDs for missing data
        uuids.update({
            'currency_uuid': str(uuid.uuid4()),
            'document_series_uuid': str(uuid.uuid4()),
            'staff_uuid': str(uuid.uuid4()),
            'payment_terms_uuid': str(uuid.uuid4()),
            'department_uuid': str(uuid.uuid4()),
            'case_uuid': str(uuid.uuid4()),
            'contract_uuid': str(uuid.uuid4()),
            'area_uuid': str(uuid.uuid4()),
            'fee_uuid': str(uuid.uuid4()),
            'cost_center_uuid': str(uuid.uuid4())
        })
        
        print(f"✅ Generated {len(uuids)} UUIDs total")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None
    finally:
        conn.close()
    
    return uuids

if __name__ == "__main__":
    print("🔍 Extracting basic UUIDs...")
    uuids = get_basic_uuids()
    
    if uuids:
        print("\n" + "="*50)
        print("📋 EXTRACTED UUIDS:")
        print("="*50)
        for key, value in uuids.items():
            print(f"{key}: {value}")
        
        # Save for use in API file
        import json
        with open('extracted_uuids.json', 'w') as f:
            json.dump(uuids, f, indent=2)
        print(f"\n💾 Saved to extracted_uuids.json")
    else:
        print("❌ Failed to extract UUIDs")
