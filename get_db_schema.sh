#!/usr/bin/env python
"""
Script để lấy schema của database (tên bảng và fields) và ghi vào file txt
Hỗ trợ cả SQLite (development) và PostgreSQL (production)
"""

import os
import sys
import django
from pathlib import Path

# Thêm đường dẫn đến Django project
sys.path.append(os.path.join(os.path.dirname(__file__), "erp-be"))

# Thiết lập Django settings
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "x_erp.settings")
django.setup()

from django.db import connection
from django.apps import apps
from django.conf import settings
from datetime import datetime


def get_database_info():
    """Lấy thông tin về database đang sử dụng"""
    db_engine = settings.DATABASES["default"]["ENGINE"]
    db_name = settings.DATABASES["default"]["NAME"]

    if "sqlite" in db_engine:
        return "SQLite", db_name
    elif "postgresql" in db_engine:
        return "PostgreSQL", db_name
    elif "mysql" in db_engine:
        return "MySQL", db_name
    else:
        return "Unknown", db_name


def get_table_schema_from_db():
    """Lấy schema trực tiếp từ database"""
    with connection.cursor() as cursor:
        db_engine = settings.DATABASES["default"]["ENGINE"]

        if "sqlite" in db_engine:
            # SQLite query để lấy tất cả bảng
            cursor.execute(
                """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name;
            """
            )
            tables = [row[0] for row in cursor.fetchall()]

            schema_info = {}
            for table in tables:
                cursor.execute(f"PRAGMA table_info([{table}]);")
                columns = cursor.fetchall()
                schema_info[table] = [
                    {
                        "name": col[1],
                        "type": col[2],
                        "not_null": bool(col[3]),
                        "default": col[4],
                        "primary_key": bool(col[5]),
                    }
                    for col in columns
                ]

        elif "postgresql" in db_engine:
            # PostgreSQL query để lấy tất cả bảng
            cursor.execute(
                """
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """
            )
            tables = [row[0] for row in cursor.fetchall()]

            schema_info = {}
            for table in tables:
                cursor.execute(
                    """
                    SELECT
                        column_name,
                        data_type,
                        is_nullable,
                        column_default,
                        CASE WHEN constraint_type = 'PRIMARY KEY' THEN true ELSE false END as is_primary_key
                    FROM information_schema.columns c
                    LEFT JOIN information_schema.key_column_usage kcu
                        ON c.table_name = kcu.table_name AND c.column_name = kcu.column_name
                    LEFT JOIN information_schema.table_constraints tc
                        ON kcu.constraint_name = tc.constraint_name
                    WHERE c.table_name = %s
                    ORDER BY c.ordinal_position;
                """,
                    [table],
                )

                columns = cursor.fetchall()
                schema_info[table] = [
                    {
                        "name": col[0],
                        "type": col[1],
                        "not_null": col[2] == "NO",
                        "default": col[3],
                        "primary_key": col[4] if col[4] is not None else False,
                    }
                    for col in columns
                ]
        else:
            raise Exception(f"Database engine {db_engine} không được hỗ trợ")

    return schema_info


def get_django_models_schema():
    """Lấy schema từ Django models"""
    schema_info = {}

    for app_config in apps.get_app_configs():
        for model in app_config.get_models():
            table_name = model._meta.db_table
            fields = []

            for field in model._meta.get_fields():
                if hasattr(field, "column"):  # Chỉ lấy các field có column trong DB
                    field_info = {
                        "name": field.column,
                        "type": field.get_internal_type(),
                        "not_null": not field.null if hasattr(field, "null") else True,
                        "default": getattr(field, "default", None),
                        "primary_key": (
                            field.primary_key
                            if hasattr(field, "primary_key")
                            else False
                        ),
                        "foreign_key": (
                            field.related_model._meta.db_table
                            if hasattr(field, "related_model") and field.related_model
                            else None
                        ),
                    }
                    fields.append(field_info)

            if fields:  # Chỉ thêm nếu có fields
                schema_info[table_name] = fields

    return schema_info


def write_schema_to_file(schema_info, filename="db_schema.txt", method="database"):
    """Ghi schema vào file txt"""
    db_type, db_name = get_database_info()

    with open(filename, "w", encoding="utf-8") as f:
        f.write("=" * 80 + "\n")
        f.write(f"DATABASE SCHEMA REPORT\n")
        f.write("=" * 80 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Database Type: {db_type}\n")
        f.write(f"Database Name: {db_name}\n")
        f.write(f"Method: {method.title()}\n")
        f.write(f"Total Tables: {len(schema_info)}\n")
        f.write("=" * 80 + "\n\n")

        for table_name, fields in sorted(schema_info.items()):
            f.write(f"TABLE: {table_name}\n")
            f.write("-" * 60 + "\n")
            f.write(
                f"{'Field Name':<25} {'Type':<20} {'Null':<8} {'Key':<8} {'Default':<15}\n"
            )
            f.write("-" * 60 + "\n")

            for field in fields:
                null_str = "YES" if not field["not_null"] else "NO"
                key_str = "PRI" if field["primary_key"] else ""
                if field.get("foreign_key"):
                    key_str = "FK"

                default_str = (
                    str(field["default"]) if field["default"] is not None else ""
                )
                if len(default_str) > 14:
                    default_str = default_str[:11] + "..."

                f.write(
                    f"{field['name']:<25} {field['type']:<20} {null_str:<8} {key_str:<8} {default_str:<15}\n"
                )

            f.write(f"\nTotal fields: {len(fields)}\n")
            f.write("\n" + "=" * 80 + "\n\n")


def main():
    """Hàm chính"""
    print("🔍 Đang lấy schema database...")

    try:
        # Thử lấy schema từ database trước
        print("📊 Đang lấy schema từ database...")
        schema_info = get_table_schema_from_db()
        method = "database"
        print(f"✅ Đã lấy được {len(schema_info)} bảng từ database")

    except Exception as e:
        print(f"⚠️  Không thể lấy schema từ database: {e}")
        print("📋 Đang lấy schema từ Django models...")

        try:
            schema_info = get_django_models_schema()
            method = "django_models"
            print(f"✅ Đã lấy được {len(schema_info)} bảng từ Django models")
        except Exception as e2:
            print(f"❌ Lỗi khi lấy schema từ Django models: {e2}")
            return

    # Ghi vào file
    output_file = "db_schema.txt"
    print(f"📝 Đang ghi schema vào file {output_file}...")

    try:
        write_schema_to_file(schema_info, output_file, method)
        print(f"✅ Đã ghi schema thành công vào file {output_file}")
        print(f"📊 Tổng số bảng: {len(schema_info)}")

        # Hiển thị một số thống kê
        total_fields = sum(len(fields) for fields in schema_info.values())
        print(f"📊 Tổng số fields: {total_fields}")

        if schema_info:
            largest_table = max(schema_info.items(), key=lambda x: len(x[1]))
            print(
                f"📊 Bảng có nhiều fields nhất: {largest_table[0]} ({len(largest_table[1])} fields)"
            )

    except Exception as e:
        print(f"❌ Lỗi khi ghi file: {e}")


if __name__ == "__main__":
    main()
