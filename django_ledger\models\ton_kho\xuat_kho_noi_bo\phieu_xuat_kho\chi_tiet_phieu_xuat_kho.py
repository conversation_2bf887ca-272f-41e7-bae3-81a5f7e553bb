"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatKho (Warehouse Export Detail) model implementation.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ChiTietPhieuXuatKhoModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ChiTietPhieuXuatKhoModel.
    """

    def for_phieu_xuat_kho(self, phieu_xuat_kho_id):  # noqa: C901
        """
        Returns details for a specific warehouse export.

        Parameters
        ----------
        phieu_xuat_kho_id: UUID
            The PhieuXuatKhoModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuXuatKhoModelQueryset
            A QuerySet of ChiTietPhieuXuatKhoModel with applied filters.
        """
        return self.filter(phieu_xuat_kho_id=phieu_xuat_kho_id)


class ChiTietPhieuXuatKhoModelManager(Manager):
    """
    A custom defined ChiTietPhieuXuatKhoModel Manager that will act as an interface to handle the  # noqa: E501
    ChiTietPhieuXuatKhoModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ChiTietPhieuXuatKhoModelQueryset.
        """
        return ChiTietPhieuXuatKhoModelQueryset(self.model, using=self._db)

    def for_phieu_xuat_kho(self, phieu_xuat_kho_id):  # noqa: C901
        """
        Returns details for a specific warehouse export.

        Parameters
        ----------
        phieu_xuat_kho_id: UUID
            The PhieuXuatKhoModel UUID used for filtering the QuerySet.

        Returns
        -------
        ChiTietPhieuXuatKhoModelQueryset
            A QuerySet of ChiTietPhieuXuatKhoModel with applied filters.
        """
        return self.get_queryset().for_phieu_xuat_kho(
            phieu_xuat_kho_id=phieu_xuat_kho_id
        )


class ChiTietPhieuXuatKhoModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ChiTietPhieuXuatKhoModel database will inherit from.  # noqa: E501
    The ChiTietPhieuXuatKhoModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501

    phieu_xuat_kho: PhieuXuatKhoModel
        The PhieuXuatKhoModel this detail belongs to.

    line: int
        The line number.

    ma_vt: VatTuModel
        The item code.

    dvt: DonViTinhModel
        The unit of measure.

    ten_dvt: str
        The unit of measure name.

    ma_kho: KhoHangModel
        The warehouse code.

    ten_kho: str
        The warehouse name.

    ma_lo: LoModel
        The batch code.

    ten_lo: str
        The batch name.

    lo_yn: int
        The batch Y/N.

    ma_vi_tri: ViTriModel
        The location code.

    ten_vi_tri: str
        The location name.

    vi_tri_yn: int
        The location Y/N.

    he_so: decimal
        The conversion ratio.

    qc_yn: int
        The QC Y/N.

    so_luong: decimal
        The quantity.

    px_dd: int
        The approved export.

    gia_nt: decimal
        The price in foreign currency.

    tien_nt: decimal
        The amount in foreign currency.

    gia: decimal
        The price.

    tien: decimal
        The amount.

    tk_vt: AccountModel
        The item account.

    ma_nx: NhapXuatModel
        The import/export code.  # noqa: E402

    tk_du: AccountModel
        The balance account.

    ma_bp: BoPhanModel
        The department code.

    ma_vv: VuViecModel
        The case code.

    ma_hd: ContractModel
        The contract code.

    ma_dtt: DotThanhToanModel
        The payment phase code.

    ma_ku: KheUocModel
        The area code.

    ma_phi: PhiModel
        The fee code.

    ma_sp: VatTuModel
        The product code.

    ma_lsx: str
        The production order code.

    ma_cp0: ChiPhiKhongHopLeModel
        The expense code.

    sl_px: decimal
        The export quantity.

    id_px: int
        The export ID.

    line_px: int
        The export line.

    id_yc: int
        The request ID.

    line_yc: int
        The request line.

    id_nhap: int
        The import ID.  # noqa: E402

    line_nhap: int
        The import line.  # noqa: E402
    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid4,
        editable=False,
        verbose_name=_('UUID'),
        help_text=_('Unique identifier for the record'),
    )
    phieu_xuat_kho = models.ForeignKey(
        'django_ledger.PhieuXuatKhoModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_xuat_kho',
        verbose_name=_('Phiếu xuất kho'),
        help_text=_('Warehouse export this detail belongs to'),
    )
    line = models.IntegerField(
        verbose_name=_('Số dòng'), help_text=_('Line number')
    )
    ma_vt = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã vật tư'),
        help_text=_('Item code'),
    )
    dvt = models.ForeignKey(
        'django_ledger.DonViTinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đơn vị tính'),
        help_text=_('Unit of measure'),
    )
    ten_dvt = models.CharField(
        max_length=100,
        verbose_name=_('Tên đơn vị tính'),
        help_text=_('Unit of measure name'),
    )
    ma_kho = models.ForeignKey(
        'django_ledger.KhoHangModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã kho'),
        help_text=_('Warehouse code'),
    )
    ten_kho = models.CharField(
        max_length=100,
        default='',
        verbose_name=_('Tên kho'),
        help_text=_('Warehouse name'),
    )
    ma_lo = models.ForeignKey(
        'django_ledger.LoModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã lô'),
        help_text=_('Batch code'),
    )
    ten_lo = models.CharField(
        max_length=100,
        default='',
        verbose_name=_('Tên lô'),
        help_text=_('Batch name'),
    )
    lo_yn = models.IntegerField(
        default=0,
        verbose_name=_('Lô Y/N'), help_text=_('Batch Y/N')
    )
    ma_vi_tri = models.ForeignKey(
        'django_ledger.ViTriModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã vị trí'),
        help_text=_('Location code'),
    )
    ten_vi_tri = models.CharField(
        max_length=100,
        default='',
        verbose_name=_('Tên vị trí'),
        help_text=_('Location name'),
    )
    vi_tri_yn = models.IntegerField(
        default=0,
        verbose_name=_('Vị trí Y/N'), help_text=_('Location Y/N')
    )
    he_so = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=1,
        verbose_name=_('Hệ số'),
        help_text=_('Conversion ratio'),
    )
    qc_yn = models.IntegerField(
        default=0,
        verbose_name=_('QC Y/N'), help_text=_('QC Y/N')
    )
    so_luong = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Số lượng'),
        help_text=_('Quantity'),
    )
    px_dd = models.IntegerField(
        verbose_name=_('Phiếu xuất đã duyệt'),
        help_text=_('Approved export'),
    )
    gia_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Giá ngoại tệ'),
        help_text=_('Price in foreign currency'),
    )
    tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tiền ngoại tệ'),
        help_text=_('Amount in foreign currency'),
    )
    gia = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Giá'),
        help_text=_('Price'),
    )
    tien = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tiền'),
        help_text=_('Amount'),
    )
    tk_vt = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_xuat_kho_tk_vt',
        verbose_name=_('Tài khoản vật tư'),
        help_text=_('Item account'),
    )
    ma_nx = models.ForeignKey(
        'django_ledger.NhapXuatModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã nhập xuất'),
        help_text=_('Import/export code'),
    )
    tk_du = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        related_name='chi_tiet_phieu_xuat_kho_tk_du',
        verbose_name=_('Tài khoản dư'),
        help_text=_('Balance account'),
    )
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã bộ phận'),
        help_text=_('Department code'),
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã vụ việc'),
        help_text=_('Case code'),
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã hợp đồng'),
        help_text=_('Contract code'),
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã đối tượng tập hợp'),
        help_text=_('Payment phase code'),
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã khu'),
        help_text=_('Area code'),
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã phí'),
        help_text=_('Fee code'),
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.SET_NULL,
        null=True,
        related_name='chi_tiet_phieu_xuat_kho_ma_sp',
        verbose_name=_('Mã sản phẩm'),
        help_text=_('Product code'),
    )
    ma_lsx = models.CharField(
        max_length=50,
        default='',
        verbose_name=_('Mã lệnh sản xuất'),
        help_text=_('Production order code'),
    )
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.SET_NULL,
        null=True,
        verbose_name=_('Mã chi phí'),
        help_text=_('Expense code'),
    )
    sl_px = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Số lượng phiếu xuất'),
        help_text=_('Export quantity'),
    )
    id_px = models.IntegerField(
        default=0,
        verbose_name=_('ID phiếu xuất'), help_text=_('Export ID')
    )
    line_px = models.IntegerField(
        default=0,
        verbose_name=_('Dòng phiếu xuất'), help_text=_('Export line')
    )
    id_yc = models.IntegerField(
        default=0,
        verbose_name=_('ID yêu cầu'), help_text=_('Request ID')
    )
    line_yc = models.IntegerField(
        default=0,
        verbose_name=_('Dòng yêu cầu'), help_text=_('Request line')
    )
    id_nhap = models.IntegerField(
        default=0,
        verbose_name=_('ID nhập'), help_text=_('Import ID')
    )
    line_nhap = models.IntegerField(
        default=0,
        verbose_name=_('Dòng nhập'), help_text=_('Import line')
    )

    objects = ChiTietPhieuXuatKhoModelManager.from_queryset(
        ChiTietPhieuXuatKhoModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Chi Tiết Phiếu Xuất Kho')
        verbose_name_plural = _('Chi Tiết Phiếu Xuất Kho')
        indexes = [
            models.Index(fields=['phieu_xuat_kho']),
            models.Index(fields=['ma_vt']),
            models.Index(fields=['ma_kho']),
        ]

    def __str__(self):  # noqa: C901
        return f'{self.phieu_xuat_kho} - {self.line}: {self.ma_vt}'


class ChiTietPhieuXuatKhoModel(ChiTietPhieuXuatKhoModelAbstract):
    """
    Base Warehouse Export Detail Model Implementation
    """

    class Meta(ChiTietPhieuXuatKhoModelAbstract.Meta):
        abstract = False
        db_table = 'chi_tiet_phieu_xuat_kho'
