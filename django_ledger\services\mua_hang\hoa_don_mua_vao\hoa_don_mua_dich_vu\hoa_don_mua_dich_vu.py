"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don Mua Dich Vu (Purchase Service Invoice) Service.
"""

from typing import Dict, Any, Optional, Union, List
from uuid import UUID
from django.db import transaction
from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuRepository
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ChiTietHoaDonMuaDichVuRepository
from django_ledger.repositories.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import <PERSON>hueHoaDonMuaDichVuRepository
from django_ledger.services.base import BaseService
from django_ledger.utils_new.debt_management.cong_no_creation import CongNoCreation


class HoaDonMuaDichVuService(BaseService):
    """
    Service class for HoaDonMuaDichVuModel.
    Handles business logic for the purchase service invoice model.

    ✅ ENHANCED: Integrated with Unified Accounting Service
    ERP Expert - 20 năm kinh nghiệm
    """

    # ✅ PREDEFINED CONFIGURATION: Hóa đơn mua dịch vụ accounting mappings
    # Use Header-Detail pattern for service purchase invoices
    PURCHASE_SERVICE_ACCOUNTING_CONFIG = [
        {
            'journal_type': 'DICHVU',               # Dịch vụ
            'debit_account_field': 'tk_vt',         # Tài khoản chi phí dịch vụ - DEBIT (từ chi tiết)
            'credit_account_field': 'tk',           # Tài khoản công nợ - CREDIT (từ header)
            'debit_account_source': 'detail',       # Lấy debit account từ detail (tk_vt)
            'credit_account_source': 'header',      # Lấy credit account từ header (tk)
            'amount_field': 'tien',                 # Số tiền từ detail
            'detail_source': 'chi_tiet',            # Related name to detail (ChiTietHoaDonMuaDichVu)
            'canCreate': True                       # Default: always create entry
        },
        {
            'journal_type': 'THUE',                 # Thuế
            'debit_account_field': 'tk_ts',         # Tài khoản tài sản - DEBIT (từ thue)
            'credit_account_field': 'tk',           # Tài khoản công nợ - CREDIT (từ header)
            'debit_account_source': 'thue',         # Lấy debit account từ thue (tk_ts)
            'credit_account_source': 'header',      # Lấy credit account từ header (tk)
            'amount_field': 'nguyen_gia',           # Nguyên giá từ thue
            'detail_source': 'thue',                # Related name to thue (ThueHoaDonMuaDichVu)
            'canCreate': True                       # Default: always create entry
        }
    ]

    def __init__(self):
        """
        Initialize the service with the repository.
        """
        super().__init__()
        self.repository = HoaDonMuaDichVuRepository()
        self.chi_tiet_repository = ChiTietHoaDonMuaDichVuRepository()
        self.thue_repository = ThueHoaDonMuaDichVuRepository()
        self._cong_no_service = CongNoCreation()

    def _determine_accounting_mappings(self, hoa_don: HoaDonMuaDichVuModel) -> List[Dict[str, Any]]:
        """
        ✅ BUSINESS LOGIC: Xác định accounting mappings dựa trên status và business rules.

        ERP Expert Logic - 20 năm kinh nghiệm:
        - DICHVU cho hóa đơn mua dịch vụ
        - THUE cho thuế tài sản
        - Conditional accounting entry creation dựa trên document status
        - Support complex approval workflows

        Parameters
        ----------
        hoa_don : HoaDonMuaDichVuModel
            Hóa đơn mua dịch vụ để analyze

        Returns
        -------
        List[Dict[str, Any]]
            Danh sách accounting mappings với canCreate được set theo business logic
        """
        # Get base configuration
        mappings = self.PURCHASE_SERVICE_ACCOUNTING_CONFIG.copy()

        # ✅ BUSINESS LOGIC: Determine canCreate based on invoice status and business rules

        # Example business logic - customize theo requirements
        if hasattr(hoa_don, 'status'):
            status = getattr(hoa_don, 'status', '1')  # Default APPROVED for backward compatibility

            if status in ['0', '4']:
                # Draft/Pending invoices - không tạo bút toán
                for mapping in mappings:
                    mapping['canCreate'] = False

            elif status in ['1', '2']:
                # Approved invoices - tạo đầy đủ bút toán
                for mapping in mappings:
                    mapping['canCreate'] = True

            else:
                # Unknown status - default behavior
                for mapping in mappings:
                    mapping['canCreate'] = True
        else:
            # No status field - default behavior (backward compatibility)
            for mapping in mappings:
                mapping['canCreate'] = True

        # ✅ ADDITIONAL BUSINESS RULES: Add more complex logic here

        # Example: Skip tax entry nếu không có thuế tài sản
        if hasattr(hoa_don, 'thue'):
            try:
                thue_list = hoa_don.thue.all()
                has_tax = any(getattr(th, 'nguyen_gia', 0) > 0 for th in thue_list)

                if not has_tax:
                    for mapping in mappings:
                        if mapping['journal_type'] == 'THUE':
                            mapping['canCreate'] = False
            except:
                pass  # Ignore errors in business logic evaluation

        return mappings

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[HoaDonMuaDichVuModel]:
        """
        Retrieves a HoaDonMuaDichVuModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to retrieve.

        Returns
        -------
        Optional[HoaDonMuaDichVuModel]
            The HoaDonMuaDichVuModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet[HoaDonMuaDichVuModel]:
        """
        Lists HoaDonMuaDichVuModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet[HoaDonMuaDichVuModel]
            A QuerySet of HoaDonMuaDichVuModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def _set_default_values(self, data: Dict[str, Any]) -> None:
        """
        Set default values for required fields if not provided.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to modify with default values.
        """
        # Set default values for required fields
        if 'ma_ngv' not in data or not data['ma_ngv']:
            data['ma_ngv'] = 'DEFAULT'

        # Document fields
        if 'so_ct0' not in data or not data['so_ct0']:
            data['so_ct0'] = data.get('so_ct', 'DEFAULT')
        if 'so_ct2' not in data or not data['so_ct2']:
            data['so_ct2'] = data.get('so_ct', 'DEFAULT')
        if 'ngay_ct0' not in data or not data['ngay_ct0']:
            data['ngay_ct0'] = data.get('ngay_ct', '2025-01-01')

        # Status and other required fields
        if 'ty_gia' not in data:
            data['ty_gia'] = 1.0
        if 'status' not in data or not data['status']:
            data['status'] = '1'
        if 'transfer_yn' not in data or not data['transfer_yn']:
            data['transfer_yn'] = 'N'

        # Financial totals
        if 't_thue_nt' not in data:
            data['t_thue_nt'] = 0.0
        if 't_thue' not in data:
            data['t_thue'] = 0.0
        if 't_tien_nt' not in data:
            data['t_tien_nt'] = 0.0
        if 't_tien' not in data:
            data['t_tien'] = 0.0
        if 't_ck_nt_ex' not in data:
            data['t_ck_nt_ex'] = 0.0
        if 't_ck_ex' not in data:
            data['t_ck_ex'] = 0.0
        if 't_ck_nt' not in data:
            data['t_ck_nt'] = 0.0
        if 't_ck' not in data:
            data['t_ck'] = 0.0
        if 't_tt_nt' not in data:
            data['t_tt_nt'] = 0.0
        if 't_tt' not in data:
            data['t_tt'] = 0.0

    @transaction.atomic()
    def create(self, entity_slug: str, data: Dict[str, Any]) -> HoaDonMuaDichVuModel:
        """
        Creates a new HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new HoaDonMuaDichVuModel.

        Returns
        -------
        HoaDonMuaDichVuModel
            The created HoaDonMuaDichVuModel instance.
        """
        # Set default values for required fields
        self._set_default_values(data)

        # Process related data
        chi_tiet_items = data.pop('chi_tiet', [])
        thue_items = data.pop('thue', [])

        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Create the HoaDonMuaDichVuModel instance
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Process chi_tiet data
        if chi_tiet_items:
            for chi_tiet_data in chi_tiet_items:
                chi_tiet_data['hoa_don'] = instance.uuid
                chi_tiet_data = self.chi_tiet_repository.convert_uuids_to_model_instances(chi_tiet_data)
                self.chi_tiet_repository.create(entity_slug=entity_slug, data=chi_tiet_data)

        # Process thue data
        if thue_items:
            for thue_data in thue_items:
                thue_data['hoa_don'] = instance.uuid
                thue_data = self.thue_repository.convert_uuids_to_model_instances(thue_data)
                self.thue_repository.create(entity_slug=entity_slug, data=thue_data)

        # ✅ UNIFIED ACCOUNTING: Tạo bút toán kế toán
        # Note: HoaDonMuaDichVuModel doesn't have ledger field like PhieuThuModel
        # Create accounting entry for the invoice
        try:
            self.create_debt_entry(instance)
        except Exception as e:
            # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
            # to maintain data consistency between HoaDonMuaDichVu and accounting entries
            raise Exception(f"Failed to create accounting entry for HoaDonMuaDichVu {instance.so_ct}: {str(e)}") from e

        return instance

    @transaction.atomic()
    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[HoaDonMuaDichVuModel]:
        """
        Updates an existing HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to update.
        data : Dict[str, Any]
            The data to update the HoaDonMuaDichVuModel with.

        Returns
        -------
        Optional[HoaDonMuaDichVuModel]
            The updated HoaDonMuaDichVuModel instance, or None if not found.
        """
        # Check existence before update
        existing_record = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not existing_record:
            raise ValueError(f"Record with UUID {uuid} not found")

        # Process related data
        chi_tiet_items = data.pop('chi_tiet', [])
        thue_items = data.pop('thue', [])

        # Convert UUIDs to model instances
        data = self.repository.convert_uuids_to_model_instances(data)

        # Update the HoaDonMuaDichVuModel instance
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        if instance:
            # Process chi_tiet data
            if chi_tiet_items:
                for chi_tiet_data in chi_tiet_items:
                    chi_tiet_data['hoa_don'] = instance.uuid
                    chi_tiet_data = self.chi_tiet_repository.convert_uuids_to_model_instances(chi_tiet_data)
                    if 'uuid' in chi_tiet_data:
                        self.chi_tiet_repository.update(entity_slug=entity_slug, uuid=chi_tiet_data['uuid'], data=chi_tiet_data)
                    else:
                        self.chi_tiet_repository.create(entity_slug=entity_slug, data=chi_tiet_data)

            # Process thue data
            if thue_items:
                for thue_data in thue_items:
                    thue_data['hoa_don'] = instance.uuid
                    thue_data = self.thue_repository.convert_uuids_to_model_instances(thue_data)
                    if 'uuid' in thue_data:
                        self.thue_repository.update(entity_slug=entity_slug, uuid=thue_data['uuid'], data=thue_data)
                    else:
                        self.thue_repository.create(entity_slug=entity_slug, data=thue_data)

            # ✅ UNIFIED ACCOUNTING: Cập nhật bút toán kế toán
            # Only update accounting if ledger exists
            if instance.ledger:
                try:
                    self.update_debt_entry(instance)
                except Exception as e:
                    # ⚠️ CRITICAL: Accounting failure should fail the entire transaction
                    # to maintain data consistency between HoaDonMuaDichVu and accounting entries
                    raise Exception(f"Failed to update accounting entry for HoaDonMuaDichVu {instance.so_ct}: {str(e)}") from e
            # Note: If no ledger exists, skip accounting update

        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a HoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the HoaDonMuaDichVuModel to delete.

        Returns
        -------
        bool
            True if the HoaDonMuaDichVuModel was deleted, False otherwise.
        """
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_child_data(self, entity_slug: str, parent_uuid=None, user_model=None):
        """
        Get child data for serializer use.
        Instead of letting serializer query DB directly, service will fetch data and provide it to serializer.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        parent_uuid : Union[str, UUID], optional
            UUID of the parent object (if any)
        user_model : UserModel, optional
            The user model to check permissions.

        Returns
        -------
        Dict[str, Dict]
            Dictionary with parent UUID as key, dict of child objects as value
        """
        result = {}

        if parent_uuid:
            # Case: Get data for a specific parent object
            if user_model:
                chi_tiet_items = self.chi_tiet_repository.get_by_parent_with_user(
                    entity_slug=entity_slug,
                    parent_uuid=parent_uuid,
                    user_model=user_model
                )
                thue_items = self.thue_repository.get_by_parent_with_user(
                    entity_slug=entity_slug,
                    parent_uuid=parent_uuid,
                    user_model=user_model
                )
            else:
                chi_tiet_items = self.chi_tiet_repository.get_by_parent(
                    entity_slug=entity_slug,
                    parent_uuid=parent_uuid
                )
                thue_items = self.thue_repository.get_by_parent(
                    entity_slug=entity_slug,
                    parent_uuid=parent_uuid
                )
            result[str(parent_uuid)] = {
                'chi_tiet': chi_tiet_items,
                'thue': thue_items
            }
        else:
            # Case: Get data for multiple parent objects (list view)
            # Get all parent objects
            if user_model:
                parents = self.repository.list(entity_slug=entity_slug, user_model=user_model)
                all_chi_tiet = self.chi_tiet_repository.list(entity_slug=entity_slug, user_model=user_model)
                all_thue = self.thue_repository.list(entity_slug=entity_slug, user_model=user_model)
            else:
                parents = self.repository.list(entity_slug=entity_slug)
                all_chi_tiet = self.chi_tiet_repository.list(entity_slug=entity_slug)
                all_thue = self.thue_repository.list(entity_slug=entity_slug)

            # Group child objects by parent_uuid
            for parent in parents:
                parent_uuid_str = str(parent.uuid)
                result[parent_uuid_str] = {
                    'chi_tiet': [
                        item for item in all_chi_tiet if str(item.hoa_don.uuid) == parent_uuid_str
                    ],
                    'thue': [
                        item for item in all_thue if str(item.hoa_don.uuid) == parent_uuid_str
                    ]
                }

        return result

    def create_debt_entry(self, hoa_don: HoaDonMuaDichVuModel) -> bool:
        """
        ✅ ENHANCED: Tạo bút toán kế toán cho hóa đơn mua dịch vụ.

        Sử dụng Unified Accounting Service theo chuẩn từ Use_hach_toan_guide.md.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - DICHVU journal type cho hóa đơn mua dịch vụ
        - THUE journal type cho thuế tài sản
        - Support cả chi tiết và thuế trong một lần gọi
        - Consistent audit trail management
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonMuaDichVuModel
            Hóa đơn mua dịch vụ để tạo bút toán kế toán.

        Returns
        -------
        bool
            True nếu tạo bút toán thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine accounting mappings for service purchase
            account_mappings = self._determine_accounting_mappings(hoa_don)

            # Check if any mapping allows creation
            can_create_any = any(mapping.get('canCreate', True) for mapping in account_mappings)

            if not can_create_any:
                return True  # Return success but skip creation

            return self._cong_no_service.create_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn mua dịch vụ",
                account_mappings=account_mappings
            )

        except Exception as e:
            # ✅ FAIL FAST: Thông báo lỗi rõ ràng để dễ debug
            raise Exception(f"Lỗi tạo bút toán hóa đơn mua dịch vụ {hoa_don.so_ct}: {str(e)}") from e

    def update_debt_entry(self, hoa_don: HoaDonMuaDichVuModel) -> bool:
        """
        ✅ ENHANCED: Cập nhật bút toán kế toán cho hóa đơn mua dịch vụ.

        Sử dụng Unified Accounting Service theo chuẩn từ Use_hach_toan_guide.md.
        ERP Expert Logic - 20 năm kinh nghiệm:
        - Update existing accounting entries
        - Maintain audit trail consistency
        - Transaction safety với rollback

        Parameters
        ----------
        hoa_don : HoaDonMuaDichVuModel
            Hóa đơn mua dịch vụ để cập nhật bút toán kế toán.

        Returns
        -------
        bool
            True nếu cập nhật bút toán thành công, False nếu thất bại.
        """
        try:
            # ✅ BUSINESS LOGIC: Determine accounting mappings for service purchase
            account_mappings = self._determine_accounting_mappings(hoa_don)

            return self._cong_no_service.update_document_accounting_entries(
                source_document=hoa_don,
                document_type="hóa đơn mua dịch vụ",
                account_mappings=account_mappings
            )

        except Exception as e:
            # ✅ FAIL FAST: Thông báo lỗi rõ ràng để dễ debug
            raise Exception(f"Lỗi cập nhật bút toán hóa đơn mua dịch vụ {hoa_don.so_ct}: {str(e)}") from e
