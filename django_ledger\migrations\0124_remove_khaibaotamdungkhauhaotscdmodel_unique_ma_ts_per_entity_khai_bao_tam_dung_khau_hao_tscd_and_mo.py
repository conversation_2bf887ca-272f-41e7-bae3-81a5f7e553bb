# Generated by Django 4.2.10 on 2025-07-01 03:51

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0123_remove_phieunhaphangbantralaimodel_phieu_nhap__ma_nk_i_c4ccf0_idx_and_more'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='khaibaotamdungkhauhaotscdmodel',
            name='unique_ma_ts_per_entity_khai_bao_tam_dung_khau_hao_tscd',
        ),
        migrations.AlterField(
            model_name='khaibaotamdungkhauhaotscdmodel',
            name='ngay_hl_tu',
            field=models.DateField(default=django.utils.timezone.now, help_text='Effective date from', verbose_name='Ngày hiệu lực từ'),
            preserve_default=False,
        ),
        migrations.AddConstraint(
            model_name='khaibaotamdungkhauhaotscdmodel',
            constraint=models.UniqueConstraint(fields=('ma_ts', 'ngay_hl_tu'), name='unique_ma_ts_ngay_hl_tu_khai_bao_tam_dung_khau_hao_tscd'),
        ),
    ]
