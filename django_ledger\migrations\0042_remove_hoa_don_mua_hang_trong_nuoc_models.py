# Generated migration to remove HoaDonMuaHangTrongNuoc models
# Created manually to clean up unused models

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0041_remove_chiphihoadonmuahangtrongnuocmodel_ten_kh_and_more'),
    ]

    operations = [
        # Remove child models first (to avoid foreign key constraint issues)
        
        # Remove ChiPhiChiTietHoaDonMuaHangTrongNuocModel
        migrations.DeleteModel(
            name='ChiPhiChiTietHoaDonMuaHangTrongNuocModel',
        ),
        
        # Remove ChiPhiHoaDonMuaHangTrongNuocModel
        migrations.DeleteModel(
            name='ChiPhiHoaDonMuaHangTrongNuocModel',
        ),
        
        # Remove ThueHoaDonMuaHangTrongNuocModel
        migrations.DeleteModel(
            name='ThueHoaDonMuaHangTrongNuocModel',
        ),
        
        # Remove ChiTietHoaDonMuaHangTrongNuocModel
        migrations.DeleteModel(
            name='ChiTietHoaDonMuaHangTrongNuocModel',
        ),
        
        # Remove main HoaDonMuaHangTrongNuocModel last
        migrations.DeleteModel(
            name='HoaDonMuaHangTrongNuocModel',
        ),
    ]
