"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

URLs for Tong Hop Nhap Xuat <PERSON> (Inventory Summary by Warehouse Report) API endpoints.
"""

from django.urls import path

from django_ledger.api.views.ton_kho.tong_hop_nhap_xuat_ton.tong_hop_nhap_xuat_ton_theo_kho import (
    TongHopNhapXuatTonTheoKhoViewSet,
)

# URL patterns - Single endpoint for inventory summary report with filters as POST body data
urlpatterns = [
    # Inventory Summary by Warehouse Report endpoint - returns report directly with filter POST body data
    path(
        "",
        TongHopNhapXuatTonTheoKhoViewSet.as_view({"post": "get_report"}),
        name="tong-hop-nhap-xuat-ton-theo-kho-report",
    ),
]
