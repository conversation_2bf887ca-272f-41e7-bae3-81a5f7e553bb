import os
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.http import JsonResponse
from django.urls import include, path
from drf_spectacular.views import (
    SpectacularAPIView,
    SpectacularRedocView,
    SpectacularSwaggerView,
)
from rest_framework.authtoken.views import obtain_auth_token

from django_ledger.settings import (  # noqa: E402
    DJANGO_LEDGER_GRAPHQL_SUPPORT_ENABLED,
)

# Import test view
from test_simple_view import SimpleTestView


def health_check(request):
    """Enhanced health check endpoint for AWS App Runner with git and environment info."""
    return JsonResponse({
        'status': 'healthy',
        'service': 'Django ERP Backend',
        'version': '1.0.0',
        'environment': os.getenv('DJANGO_ENVIRONMENT', 'development'),
        'git_hash': os.getenv('GIT_COMMIT_HASH', 'unknown'),
        'git_commit_message': os.getenv('GIT_COMMIT_MESSAGE', 'unknown'),
        'build_timestamp': os.getenv('BUILD_TIMESTAMP', 'unknown'),
        'deployment_id': os.getenv('DEPLOYMENT_ID', 'unknown')
    })

urlpatterns = [  # noqa: F841
    # Health check endpoint for AWS App Runner
    path('health/', health_check, name='health_check'),
    path('thayminhtue/', admin.site.urls),
    path(
        '', include('django_ledger.urls', namespace='django_ledger')
    ),  # noqa: F841
    path('api/', include('django_ledger.urls.djl_api')),
    # Test endpoint
    path('api/test/entities/<str:entity_slug>/hoa-don-mua-dich-vu/', SimpleTestView.as_view(), name='test_hoa_don_mua_dich_vu'),
    # Authentication
    path(
        'api/auth/token/', obtain_auth_token, name='api_token_auth'
    ),  # noqa: F841
    path('api/auth/', include('rest_framework.urls')),
    # API Documentation
    path(
        'api/schema/', SpectacularAPIView.as_view(), name='schema'
    ),  # noqa: F841
    path(
        'api/schema/swagger-ui/',
        SpectacularSwaggerView.as_view(url_name='schema'),  # noqa: F841
        name='swagger-ui',  # noqa: F841
    ),
    path(
        'api/schema/redoc/',
        SpectacularRedocView.as_view(url_name='schema'),  # noqa: F841
        name='redoc',  # noqa: F841
    ),
    # SQL Explorer
    path('explorer/', include('explorer.urls')),
]

# GraphQl API Support...
try:
    if DJANGO_LEDGER_GRAPHQL_SUPPORT_ENABLED:
        from django_ledger.contrib.django_ledger_graphene.api import (  # noqa: E402
            schema,
        )
        from django_ledger.contrib.django_ledger_graphene.views import (  # noqa: E402
            DjangoLedgerOAuth2GraphQLView,
        )

        urlpatterns += [  # noqa: F841
            path(
                'api/v1/graphql/',
                DjangoLedgerOAuth2GraphQLView.as_view(
                    graphiql=settings.DEBUG, schema=schema
                ),  # noqa: F841
            ),
            path(
                'api/v1/o/',
                include(
                    'oauth2_provider.urls', namespace='oauth2_provider'
                ),  # noqa: F841
            ),
        ]

except ImportError:
    pass

# Serve static files during development
if settings.DEBUG:
    urlpatterns += static(
        settings.STATIC_URL, document_root=settings.STATIC_ROOT
    )  # noqa: F841
