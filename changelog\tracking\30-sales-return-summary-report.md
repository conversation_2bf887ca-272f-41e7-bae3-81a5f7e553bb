# Sales Return Summary Report Implementation

## Task: Create Django implementation for "ban-hang/bao-cao-ban-hang/bao-cao-tong-hop-hang-ban-tra-lai"

### Requirements Analysis
- [ ] Create View class following existing patterns
- [ ] Create Serializer class following existing patterns  
- [ ] Create Service class with only `get_report` method
- [ ] Follow existing codebase patterns for serializers, views, and services implementation
- [ ] No URL prefixes or additional methods beyond what's requested

### Input Parameters
```
ngay_ct1          # Date range start
ngay_ct2          # Date range end  
so_ct1            # Document number range start
so_ct2            # Document number range end
ma_nvbh           # UUID model - Sales staff
ma_kh             # UUID model - Customer
nh_kh1            # UUID model - Customer group 1
nh_kh2            # UUID model - Customer group 2
nh_kh3            # UUID model - Customer group 3
rg_code           # UUID model - Region code
ma_vt             # UUID model - Item code
ma_lvt            # UUID model - Item type
ton_kho_yn        # Inventory flag
nh_vt1            # UUID model - Item group 1
nh_vt2            # UUID model - Item group 2
nh_vt3            # UUID model - Item group 3
ma_kho            # UUID model - Warehouse
ma_unit           # UUID model - Unit
loai_du_lieu      # Data type
mau_bc            # Text - Report template
group_by          # Grouping option
ma_gd             # UUID model - Transaction
tk_vt             # UUID model - Item account
tk_dt             # UUID model - Revenue account
tk_gv             # UUID model - COGS account
ma_lo             # UUID model - Lot
ma_vi_tri         # UUID model - Location
dien_giai         # Description
data_analysis_struct  # Text - Data analysis structure
```

### Implementation Plan

#### 1. Create Serializer
- [x] Create request serializer with all input parameters
- [x] Create response serializer for report data
- [x] Follow existing patterns from similar reports
- [x] Add proper validation for date ranges and UUIDs

#### 2. Create Service
- [x] Create service class inheriting from appropriate base
- [x] Implement only `get_report` method as specified
- [x] Follow transaction report service patterns
- [ ] Enhance with raw SQL query following CCDC allocation pattern

#### 3. Create View
- [x] Create ViewSet following existing patterns
- [x] Implement `get_report` method handling POST requests
- [x] Add proper authentication and pagination
- [x] Follow existing error handling patterns

#### 4. Enhanced Implementation (CCDC Pattern)
- [ ] Examine existing CCDC allocation table pattern
- [ ] Create repository method in Phiếu nhập hàng bán trả lại service
- [ ] Implement raw SQL query targeting sales return receipt model
- [ ] Modify BaoCaoTongHopHangBanTraLaiService to use service-to-service communication
- [ ] Ensure query handles all input parameters from serializer
- [ ] Return data in format expected by response serializer

#### 4. File Structure
```
erp-be/django_ledger/api/serializers/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/
├── __init__.py
└── bao_cao_tong_hop_hang_ban_tra_lai.py

erp-be/django_ledger/api/views/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/
├── __init__.py  
└── bao_cao_tong_hop_hang_ban_tra_lai.py

erp-be/django_ledger/services/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/
├── __init__.py
└── bao_cao_tong_hop_hang_ban_tra_lai.py
```

### Progress Tracking
- [x] Analyze existing patterns and structure
- [x] Create serializer files
- [x] Create service files
- [x] Create view files
- [x] Create router/URL configuration
- [x] Initial implementation complete
- [x] Implement raw SQL query following CCDC allocation pattern
- [x] Create repository method in Phiếu nhập hàng bán trả lại service
- [x] Enhance BaoCaoTongHopHangBanTraLaiService with proper raw SQL
- [x] Implementation complete with CCDC pattern
- [ ] Update changelog

### Implementation Summary

#### Files Created:
1. **Serializer**: `erp-be/django_ledger/api/serializers/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/`
   - `__init__.py` - Module initialization
   - `bao_cao_tong_hop_hang_ban_tra_lai.py` - Request and response serializers

2. **Service**: `erp-be/django_ledger/services/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/`
   - `__init__.py` - Module initialization
   - `bao_cao_tong_hop_hang_ban_tra_lai.py` - Service with `get_report` method only

3. **View**: `erp-be/django_ledger/api/views/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/`
   - `__init__.py` - Module initialization
   - `bao_cao_tong_hop_hang_ban_tra_lai.py` - ViewSet with `get_report` method

4. **Router**: `erp-be/django_ledger/api/routers/ban_hang/bao_cao_ban_hang/bao_cao_tong_hop_hang_ban_tra_lai/`
   - `__init__.py` - Module initialization
   - `urls.py` - URL configuration for the endpoint
   - Updated main `bao_cao_ban_hang/urls.py` to include new report

#### Key Features:
- All input parameters supported as specified
- Proper UUID field handling with empty string conversion
- Date range validation
- **Raw SQL query implementation following CCDC allocation pattern**
- **Service-to-service communication architecture**
- **Repository layer for raw SQL execution**
- **Query building in BaoCaoTongHopHangBanTraLaiService**
- **PhieuNhapHangBanTraLai service with execute_raw_query method**
- Transaction-based reporting service inheritance
- Pagination support
- Error handling and logging
- Authentication required
- Follows existing codebase patterns

### Notes
- Following existing codebase patterns from similar reports
- Using transaction-based reporting approach
- Maintaining consistency with existing naming conventions
- Ensuring proper error handling and validation
- Service contains only `get_report` method as requested
- No URL prefixes or additional functionality added
