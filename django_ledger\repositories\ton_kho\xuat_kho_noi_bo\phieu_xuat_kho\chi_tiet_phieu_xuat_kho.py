"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatKho (Warehouse Export Detail) repository implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401

from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    ChiTietPhieuXuatKhoModel,
    PhieuXuatKhoModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietPhieuXuatKhoRepository(BaseRepository):
    """
    Repository class for ChiTietPhieuXuatKhoModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=ChiTietPhieuXuatKhoModel)

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietPhieuXuatKhoModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhieuXuatKhoModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatKhoModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhieuXuatKhoModel]
            The ChiTietPhieuXuatKhoModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_xuat_kho(
        self, phieu_xuat_kho_id: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Returns ChiTietPhieuXuatKhoModel instances for a specific PhieuXuatKhoModel.

        Parameters
        ----------
        phieu_xuat_kho_id : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to filter by.

        Returns
        -------
        QuerySet
            QuerySet of ChiTietPhieuXuatKhoModel instances for the specified PhieuXuatKhoModel.  # noqa: E501
        """
        return self.model_class.objects.for_phieu_xuat_kho(
            phieu_xuat_kho_id=phieu_xuat_kho_id
        )

    def list_with_filters(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> QuerySet:  # noqa: C901
        """
        Lists ChiTietPhieuXuatKhoModel instances with custom filters.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by.
        filters : Dict[str, Any]
            Dictionary of filters to apply to the queryset.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuXuatKhoModel instances with applied filters.
        """
        from django_ledger.models import EntityModel  # noqa: F401,

        # Get entity model
        try:
            entity_model = EntityModel.objects.get(slug=entity_slug)
        except EntityModel.DoesNotExist:
            return self.model_class.objects.none()

        # Start with base queryset filtered by entity
        queryset = self.model_class.objects.filter(
            phieu_xuat_kho__entity_model=entity_model
        ).select_related(
            "phieu_xuat_kho",  # PhieuXuatKho model for document info
            "ma_vt",  # VatTu model for material info
            "ma_kho",  # KhoHang model for warehouse info
            "dvt",  # DonViTinh model for unit of measure
            "ma_bp",  # BoPhan model for department info
            "ma_vv",  # Business unit model
            "ma_hd",  # Contract model
            "ma_ku",  # Region model
            "ma_phi",  # Fee model
            "ma_sp",  # Product model
            "ma_dtt",  # Payment terms model
            "ma_cp0",  # Cost center model
            "tk_vt",  # Account model for material account
            "tk_du",  # Account model for backup account
            "phieu_xuat_kho__ma_kh",  # Customer model for customer info
        )

        # Apply date filters
        if filters.get("ngay_ct1"):
            queryset = queryset.filter(
                phieu_xuat_kho__ngay_ct__gte=filters["ngay_ct1"]
            )
        if filters.get("ngay_ct2"):
            queryset = queryset.filter(
                phieu_xuat_kho__ngay_ct__lte=filters["ngay_ct2"]
            )
        # Apply other filters
        if filters.get("ma_kho"):
            queryset = queryset.filter(ma_kho__ma_kho=filters["ma_kho"])
        if filters.get("ma_vt"):
            queryset = queryset.filter(ma_vt__ma_vt=filters["ma_vt"])
        if filters.get("ma_bp"):
            queryset = queryset.filter(ma_bp__ma_bp=filters["ma_bp"])
        if filters.get("ma_vv"):
            queryset = queryset.filter(ma_vv__ma_vv=filters["ma_vv"])
        if filters.get("ma_hd"):
            queryset = queryset.filter(ma_hd__ma_hd=filters["ma_hd"])
        if filters.get("ma_ku"):
            queryset = queryset.filter(ma_ku__ma_ku=filters["ma_ku"])
        if filters.get("ma_phi"):
            queryset = queryset.filter(ma_phi__ma_phi=filters["ma_phi"])
        if filters.get("ma_dtt"):
            queryset = queryset.filter(ma_dtt__ma_dtt=filters["ma_dtt"])
        if filters.get("ma_cp0"):
            queryset = queryset.filter(ma_cp0__ma_cp0=filters["ma_cp0"])
        return queryset

    def create(
        self, phieu_xuat_kho_id: Union[str, UUID], data: Dict[str, Any]
    ) -> ChiTietPhieuXuatKhoModel:  # noqa: C901
        """
        Creates a new ChiTietPhieuXuatKhoModel instance.

        Parameters
        ----------
        phieu_xuat_kho_id : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel.
        data : Dict[str, Any]
            The data for creating the ChiTietPhieuXuatKhoModel.

        Returns
        -------
        ChiTietPhieuXuatKhoModel
            The created ChiTietPhieuXuatKhoModel instance.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get warehouse export
        try:
            phieu_xuat_kho = PhieuXuatKhoModel.objects.get(
                uuid=phieu_xuat_kho_id
            )
        except PhieuXuatKhoModel.DoesNotExist:
            raise ValueError(
                f"PhieuXuatKhoModel with UUID {phieu_xuat_kho_id} does not exist"
            )

        # Add warehouse export to data
        data['phieu_xuat_kho'] = phieu_xuat_kho
        # Create warehouse export detail
        instance = self.model_class(**data)
        instance.save()
        return instance

    def create_many(  # noqa: C901
        self,
        phieu_xuat_kho_id: Union[str, UUID],
        data_list: List[Dict[str, Any]],
    ) -> List[ChiTietPhieuXuatKhoModel]:
        """
        Creates multiple ChiTietPhieuXuatKhoModel instances.

        Parameters
        ----------
        phieu_xuat_kho_id : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel.
        data_list : List[Dict[str, Any]]
            The data for creating the ChiTietPhieuXuatKhoModel instances.

        Returns
        -------
        List[ChiTietPhieuXuatKhoModel]
            The created ChiTietPhieuXuatKhoModel instances.
        """
        from django.db import transaction  # noqa: F401,

        # Get warehouse export
        try:
            phieu_xuat_kho = PhieuXuatKhoModel.objects.get(
                uuid=phieu_xuat_kho_id
            )
        except PhieuXuatKhoModel.DoesNotExist:
            raise ValueError(
                f"PhieuXuatKhoModel with UUID {phieu_xuat_kho_id} does not exist"
            )

        # Create warehouse export details
        created_details = []
        with transaction.atomic():
            for data in data_list:
                # Convert UUIDs to model instances
                data = self.convert_uuids_to_model_instances(data)
                # Add warehouse export to data
                data['phieu_xuat_kho'] = phieu_xuat_kho
                # Create warehouse export detail
                instance = self.model_class(**data)
                instance.save()
                created_details.append(instance)

        return created_details

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiTietPhieuXuatKhoModel]:  # noqa: C901
        """
        Updates an existing ChiTietPhieuXuatKhoModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatKhoModel to update.
        data : Dict[str, Any]
            The data for updating the ChiTietPhieuXuatKhoModel.

        Returns
        -------
        Optional[ChiTietPhieuXuatKhoModel]
            The updated ChiTietPhieuXuatKhoModel instance, or None if not found.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get the instance
        instance = self.get_by_id(uuid=uuid)
        if instance:
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ChiTietPhieuXuatKhoModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatKhoModel to delete.

        Returns
        -------
        bool
            True if the ChiTietPhieuXuatKhoModel was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        ChiTietPhieuXuatKho related fields and other foreign keys.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'line',
            'ma_lsx',
            'line_yc',
        ]

        # Fields that don't exist in model and should be removed
        fields_to_remove = ['so_ct_yc']

        # Remove fields that don't exist in model
        for field_to_remove in fields_to_remove:
            data_copy.pop(field_to_remove, None)

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        return data_copy
