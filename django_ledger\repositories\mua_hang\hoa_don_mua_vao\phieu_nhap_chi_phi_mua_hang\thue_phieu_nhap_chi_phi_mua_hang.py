"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThuePhieuNhapChiPhiMuaHangRepository, which handles database operations  # noqa: E501
for the ThuePhieuNhapChiPhiMuaHangModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models import (  # noqa: F401,
    AccountModel,
    BoPhanModel,
    ChiPhiKhongHopLeModel,
    ContractModel,
    CustomerModel,
    DotThanhToanModel,
    HanThanhToanModel,
    KheUocModel,
    MauSoHDModel,
    PhiModel,
    TaxModel,
    VatTuModel,
    VuViecModel,
)
from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangModel,
    ThuePhieuNhapChiPhiMuaHangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ThuePhieuNhapChiPhiMuaHangRepository(BaseRepository):
    """
    Repository class for ThuePhieuNhapChiPhiMuaHangModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=ThuePhieuNhapChiPhiMuaHangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for ThuePhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        QuerySet
            The base queryset for ThuePhieuNhapChiPhiMuaHangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ThuePhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Retrieves a ThuePhieuNhapChiPhiMuaHangModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ThuePhieuNhapChiPhiMuaHangModel to retrieve.

        Returns
        -------
        Optional[ThuePhieuNhapChiPhiMuaHangModel]
            The ThuePhieuNhapChiPhiMuaHangModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_nhap(
        self, phieu_nhap_id: Union[str, UUID], **kwargs
    ) -> QuerySet:  # noqa: C901
        """
        Lists ThuePhieuNhapChiPhiMuaHangModel instances for a specific purchase expense receipt.  # noqa: E501

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of ThuePhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.for_phieu_nhap(
            phieu_nhap_id=phieu_nhap_id
        ).filter(**kwargs)

    def create(
        self, phieu_nhap_id: Union[str, UUID], data: Dict[str, Any]
    ) -> ThuePhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        Creates a new ThuePhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        phieu_nhap_id : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel.
        data : Dict[str, Any]
            The data for the new ThuePhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        ThuePhieuNhapChiPhiMuaHangModel
            The created ThuePhieuNhapChiPhiMuaHangModel instance.
        """
        # Get the PhieuNhapChiPhiMuaHangModel instance
        phieu_nhap = PhieuNhapChiPhiMuaHangModel.objects.get(uuid=phieu_nhap_id)
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Create the ThuePhieuNhapChiPhiMuaHangModel instance
        instance = self.model_class(phieu_nhap=phieu_nhap, **data)
        instance.save()

        return instance

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ThuePhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Updates an existing ThuePhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ThuePhieuNhapChiPhiMuaHangModel to update.
        data : Dict[str, Any]
            The data to update the ThuePhieuNhapChiPhiMuaHangModel with.

        Returns
        -------
        Optional[ThuePhieuNhapChiPhiMuaHangModel]
            The updated ThuePhieuNhapChiPhiMuaHangModel instance, or None if not found.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ThuePhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ThuePhieuNhapChiPhiMuaHangModel to delete.

        Returns
        -------
        bool
            True if the ThuePhieuNhapChiPhiMuaHangModel was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Converts UUID strings in the data to model instances.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances.
        """

        # Convert ma_thue UUID to TaxModel instance
        if 'ma_thue' in data and data['ma_thue']:
            try:
                data['ma_thue'] = TaxModel.objects.get(uuid=data['ma_thue'])
            except TaxModel.DoesNotExist:
                data['ma_thue'] = None

        # Convert ma_kh UUID to CustomerModel instance
        if 'ma_kh' in data and data['ma_kh']:
            try:
                data['ma_kh'] = CustomerModel.objects.get(uuid=data['ma_kh'])
            except CustomerModel.DoesNotExist:
                data['ma_kh'] = None

        # Convert tk_thue_no UUID to AccountModel instance
        if 'tk_thue_no' in data and data['tk_thue_no']:
            try:
                data['tk_thue_no'] = AccountModel.objects.get(
                    uuid=data['tk_thue_no']
                )
            except AccountModel.DoesNotExist:
                data['tk_thue_no'] = None

        # Convert ma_mau_ct UUID to MauSoHDModel instance
        if 'ma_mau_ct' in data and data['ma_mau_ct']:
            try:
                data['ma_mau_ct'] = MauSoHDModel.objects.get(uuid=data['ma_mau_ct'])
            except MauSoHDModel.DoesNotExist:
                data['ma_mau_ct'] = None

        # Convert tk_thue UUID to AccountModel instance
        if 'tk_thue' in data and data['tk_thue']:
            try:
                data['tk_thue'] = AccountModel.objects.get(uuid=data['tk_thue'])
            except AccountModel.DoesNotExist:
                data['tk_thue'] = None

        # Convert ma_kh9 UUID to CustomerModel instance
        if 'ma_kh9' in data and data['ma_kh9']:
            try:
                data['ma_kh9'] = CustomerModel.objects.get(uuid=data['ma_kh9'])
            except CustomerModel.DoesNotExist:
                data['ma_kh9'] = None

        # Convert ma_tt UUID to HanThanhToanModel instance
        if 'ma_tt' in data and data['ma_tt']:
            try:
                data['ma_tt'] = HanThanhToanModel.objects.get(uuid=data['ma_tt'])
            except HanThanhToanModel.DoesNotExist:
                data['ma_tt'] = None

        # Convert ma_bp UUID to BoPhanModel instance
        if 'ma_bp' in data and data['ma_bp']:
            try:
                data['ma_bp'] = BoPhanModel.objects.get(uuid=data['ma_bp'])
            except BoPhanModel.DoesNotExist:
                data['ma_bp'] = None

        # Convert ma_vv UUID to VuViecModel instance
        if 'ma_vv' in data and data['ma_vv']:
            try:
                data['ma_vv'] = VuViecModel.objects.get(uuid=data['ma_vv'])
            except VuViecModel.DoesNotExist:
                data['ma_vv'] = None

        # Convert ma_hd UUID to ContractModel instance
        if 'ma_hd' in data and data['ma_hd']:
            try:
                data['ma_hd'] = ContractModel.objects.get(uuid=data['ma_hd'])
            except ContractModel.DoesNotExist:
                data['ma_hd'] = None

        # Convert ma_dtt UUID to DotThanhToanModel instance
        if 'ma_dtt' in data and data['ma_dtt']:
            try:
                data['ma_dtt'] = DotThanhToanModel.objects.get(uuid=data['ma_dtt'])
            except DotThanhToanModel.DoesNotExist:
                data['ma_dtt'] = None

        # Convert ma_ku UUID to KheUocModel instance
        if 'ma_ku' in data and data['ma_ku']:
            try:
                data['ma_ku'] = KheUocModel.objects.get(uuid=data['ma_ku'])
            except KheUocModel.DoesNotExist:
                data['ma_ku'] = None

        # Convert ma_phi UUID to PhiModel instance
        if 'ma_phi' in data and data['ma_phi']:
            try:
                data['ma_phi'] = PhiModel.objects.get(uuid=data['ma_phi'])
            except PhiModel.DoesNotExist:
                data['ma_phi'] = None

        # Convert ma_sp UUID to VatTuModel instance
        if 'ma_sp' in data and data['ma_sp']:
            try:
                data['ma_sp'] = VatTuModel.objects.get(uuid=data['ma_sp'])
            except VatTuModel.DoesNotExist:
                data['ma_sp'] = None

        # Convert ma_cp0 UUID to ChiPhiKhongHopLeModel instance
        if 'ma_cp0' in data and data['ma_cp0']:
            try:
                data['ma_cp0'] = ChiPhiKhongHopLeModel.objects.get(uuid=data['ma_cp0'])
            except ChiPhiKhongHopLeModel.DoesNotExist:
                data['ma_cp0'] = None

        return data
