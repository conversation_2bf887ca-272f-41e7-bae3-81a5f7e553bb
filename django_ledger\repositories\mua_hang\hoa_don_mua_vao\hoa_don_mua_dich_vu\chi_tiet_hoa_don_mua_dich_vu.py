"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Chi Tiet Hoa Don Mua Dich Vu (Purchase Service Invoice Detail) Repository.
"""

from typing import Dict, Any, Optional, Union
from uuid import UUID
from django.db.models import QuerySet

from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import ChiTietHoaDonMuaDichVuModel
from django_ledger.repositories.base import BaseRepository


class ChiTietHoaDonMuaDichVuRepository(BaseRepository[ChiTietHoaDonMuaDichVuModel]):
    """
    Repository for ChiTietHoaDonMuaDichVuModel operations.
    """

    def __init__(self):
        """
        Initialize the repository with the ChiTietHoaDonMuaDichVuModel.
        """
        super().__init__(model_class=ChiTietHoaDonMuaDichVuModel)

    def get_queryset(self) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Returns the base queryset for ChiTietHoaDonMuaDichVuModel.

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            The base queryset for ChiTietHoaDonMuaDichVuModel.
        """
        return self.model_class.objects.all()

    def get_by_id(self, entity_slug: str, uuid: Union[str, UUID]) -> Optional[ChiTietHoaDonMuaDichVuModel]:
        """
        Retrieves a ChiTietHoaDonMuaDichVuModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to retrieve.

        Returns
        -------
        Optional[ChiTietHoaDonMuaDichVuModel]
            The ChiTietHoaDonMuaDichVuModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.for_entity(entity_slug=entity_slug).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Lists ChiTietHoaDonMuaDichVuModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            A QuerySet of ChiTietHoaDonMuaDichVuModel instances.
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug).filter(**kwargs)

    def get_by_parent(self, entity_slug: str, parent_uuid: Union[str, UUID]) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Get list of child objects by parent_uuid.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        parent_uuid : Union[str, UUID]
            UUID of the parent object

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            List of child objects
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug).filter(
            hoa_don__uuid=parent_uuid
        )

    def get_by_parent_with_user(self, entity_slug: str, parent_uuid: Union[str, UUID], user_model) -> QuerySet[ChiTietHoaDonMuaDichVuModel]:
        """
        Get list of child objects by parent_uuid with user permissions check.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        parent_uuid : Union[str, UUID]
            UUID of the parent object
        user_model : UserModel
            The user model to check permissions.

        Returns
        -------
        QuerySet[ChiTietHoaDonMuaDichVuModel]
            List of child objects
        """
        return self.model_class.objects.for_entity(
            entity_slug=entity_slug,
            user_model=user_model
        ).filter(hoa_don__uuid=parent_uuid)

    def create(self, parent_field, data: Dict[str, Any]) -> ChiTietHoaDonMuaDichVuModel:
        """
        Creates a new ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        parent_field : HoaDonMuaDichVuModel
            The parent HoaDonMuaDichVuModel instance.
        data : Dict[str, Any]
            The data for the new ChiTietHoaDonMuaDichVuModel.

        Returns
        -------
        ChiTietHoaDonMuaDichVuModel
            The created ChiTietHoaDonMuaDichVuModel instance.
        """
        # Convert UUIDs to model instances
        # processed_data = self.convert_uuids_to_model_instances(data.copy())  # Temporarily disabled
        processed_data = data.copy()

        # Create the instance with parent reference
        instance = self.model_class(hoa_don=parent_field, **processed_data)
        instance.save()

        return instance

    def update(self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]) -> Optional[ChiTietHoaDonMuaDichVuModel]:
        """
        Updates an existing ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietHoaDonMuaDichVuModel with.

        Returns
        -------
        Optional[ChiTietHoaDonMuaDichVuModel]
            The updated ChiTietHoaDonMuaDichVuModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:
        """
        Deletes a ChiTietHoaDonMuaDichVuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the ChiTietHoaDonMuaDichVuModel to delete.

        Returns
        -------
        bool
            True if the ChiTietHoaDonMuaDichVuModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert UUID strings to model instances for foreign key fields.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data dictionary with model instances.
        """
        from django_ledger.models.danh_muc.bo_phan import BoPhanModel
        from django_ledger.models.danh_muc.viec_viec import ViecViecModel
        from django_ledger.models.danh_muc.chi_phi import ChiPhiModel

        # Try to import optional models, skip if not available
        try:
            from django_ledger.models.danh_muc.hop_dong import HopDongModel
        except ImportError:
            HopDongModel = None
        try:
            from django_ledger.models.danh_muc.khu_vuc import KhuVucModel
        except ImportError:
            KhuVucModel = None
        try:
            from django_ledger.models.danh_muc.phi import PhiModel
        except ImportError:
            PhiModel = None

        data_copy = data.copy()

        # Convert foreign key UUIDs to model instances
        # Only convert actual ForeignKey fields, not CharField fields
        uuid_field_mappings = {}

        # Add mappings only for available models
        if BoPhanModel:
            uuid_field_mappings['ma_bp'] = BoPhanModel
        if ViecViecModel:
            uuid_field_mappings['ma_vv'] = ViecViecModel
        if HopDongModel:
            uuid_field_mappings['ma_hd'] = HopDongModel
        if KhuVucModel:
            uuid_field_mappings['ma_ku'] = KhuVucModel
        if PhiModel:
            uuid_field_mappings['ma_phi'] = PhiModel
        if ChiPhiModel:
            uuid_field_mappings['ma_cp0'] = ChiPhiModel

        # CharField fields that should NOT be converted:
        # tk_vt, ma_dtt, ma_sp, ma_lsx, ma_dv, ma_lts, ma_thue, etc.

        for field_name, model_class in uuid_field_mappings.items():
            if field_name in data_copy and isinstance(data_copy[field_name], str):
                try:
                    data_copy[field_name] = model_class.objects.get(uuid=data_copy[field_name])
                except model_class.DoesNotExist:
                    data_copy[field_name] = None

        return data_copy


