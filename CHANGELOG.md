# Changelog

All notable changes to this project will be documented in this file.

## [Unreleased]

### Fixed
- Fixed KhaiBaoTamDungKhauHaoTSCD date range validation issue
  - Simplified validation logic to focus on essential business rules
  - Fixed issue where same dates for ngay_hl_tu and ngay_hl_den were incorrectly rejected
  - Allows same-day suspension periods (ngay_hl_tu = ngay_hl_den)
  - Validates that ngay_hl_den >= ngay_hl_tu when both dates are provided
  - Maintains unique constraint checking for (ma_ts, ngay_hl_tu) combination only
  - Supports open-ended suspension periods (ngay_hl_den = None)
  - Enhanced error messages for clear user feedback
  - Resolves UNIQUE constraint failed error in cURL request with same dates
  - Applied KISS principle for maintainable and understandable validation logic
- Fixed KhaiBaoTamDungKhauHaoTSCD unique constraint error
  - Updated unique constraint from ['entity_model', 'ma_ts'] to ['ma_ts', 'ngay_hl_tu']
  - Allows multiple depreciation suspension declarations for the same asset with different effective dates
  - Prevents duplicate declarations for the same asset on the same effective date
  - Implemented validation at serializer level only (following user guidelines)
  - Removed validation logic from service and repository layers
  - Updated ngay_hl_tu field to be required (null=False, blank=False)
  - Applied database migrations:
    - 0122_update_unique_constraint_khai_bao_tam_dung_khau_hao_tscd
    - 0123_update_ngay_hl_tu_required_khai_bao_tam_dung_khau_hao_tscd
  - Resolves UNIQUE constraint failed error when creating multiple suspension declarations
  - Follows functional programming principles with comprehensive edge case handling
  - Enhanced data integrity while providing more flexible business logic

### Added
- PhieuNhapChiPhiMuaHang Required Fields Enhancement
  - Updated ma_kh (customer) field to be required (removed null=True, blank=True)
  - Updated tk (account) field to be required (removed null=True)
  - Applied database migration 0112_update_phieu_nhap_chi_phi_mua_hang_required_fields
  - Ensures data integrity for purchase expense receipt records
  - Follows existing validation patterns in the codebase
  - Backend-only changes for model field constraints

- Enhanced Group Code Validation (ma_nhom)
  - Added comprehensive backend validation for group codes (ma_nhom field)
  - Prevents any whitespace characters (spaces, tabs, newlines)
  - Blocks Unicode characters (only ASCII allowed)
  - Restricts special characters: ~!@#$%^&*(),;"<>?/\|_-.
  - Implemented validation at serializer level following Django REST Framework standards
  - Added reusable validation utility function: validate_ma_nhom_field()
  - Follows functional programming principles with pure validation functions
  - Comprehensive error messages in Vietnamese
  - Backend-only validation as requested (no frontend changes)
  - Added test script for validation verification
  - Automatic validation through DRF serialization process

### Fixed
- Fixed CapNhatDeNghiThanhToan model to use ChungTuMixin pattern
  - Refactored CapNhatDeNghiThanhToanModelAbstract to inherit from ChungTuMixIn
  - Removed direct field definitions for i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct
  - Updated serializer to use ChungTuSerializerMixin instead of explicit field definitions
  - Removed redundant validation methods (handled by ChungTuSerializerMixin)
  - Updated repository to use utility functions from chung_tu_item_utils
  - Replaced custom ChungTu field handling with reusable utility functions:
    - process_chung_tu_fields_extraction_and_conversion_extended for field extraction
    - create_instance_with_chung_tu_fields for instance creation
    - update_instance_with_chung_tu_fields for instance updates
  - Removed duplicate convert_chung_tu_uuids_to_model_instances method
  - Updated database indexes to remove redundant so_ct index (handled by ChungTuMixIn)
  - Applied database migration to restructure model fields
  - Follows the same pattern as PhieuXuatDieuChuyen model for consistency
  - Leverages reusable components for better code maintainability and consistency

### Added
- TT200 Chart of Accounts Initialization Package
  - Added init_account package for TT200 account setup
  - Created create_tt200_accounts function for programmatic CoA creation
  - Added command-line script support for account initialization
  - Full documentation with usage examples
  - Support for both Vietnamese and English account names
  - Proper account hierarchies following TT200 structure

### Fixed
- Added missing for_entity methods to model querysets
  - Added for_entity method to VatTuSanPhamDonViTinhModelQueryset
  - Added for_entity method to VatTuSanPhamHangHoaModelQueryset
  - Added for_entity method to GiaMuaModelQueryset
  - Added for_entity method to KheUocThongTinLaiSuatModelQueryset
  - Added for_entity method to KheUocChiTietThanhToanModelQueryset
  - Fixed entity field lookup from entity_model to entity in KheUoc-related models
  - Implemented consistent entity-specific filtering with proper permission checks
  - Fixed errors when accessing entity-filtered querysets
  - Maintained compatibility with existing manager-level entity filtering

- Fixed pagination in Lo (Lot) model API
  - Added proper pagination handling in repository layer
  - Updated Lo service layer to support pagination consistently
  - Changed next/previous page indicators to return 0 instead of null when no more pages
  - Improved type hints for pagination return types
  - Made pagination behavior consistent across all Lo-related endpoints

### Changed
- Enhanced pagination implementation for API endpoints
  - Implemented pagination at repository and service layers for CoQuanThue, Color and Contract models
  - Added pagination support in repositories with remaining items calculation
  - Updated services to handle pagination parameters
  - Modified viewsets to use service layer pagination
  - Page size set to 20 items with maximum of 100
  - Added support for page_size query parameter
  - Show remaining items count for next/previous instead of URLs
  - Maintained existing filter support (e.g. active status for colors, filters for contracts)

### Added
- Implemented VatTu model with Django Ledger structure
  - Added VatTuModelQueryset with filtering methods
  - Added VatTuModelManager for entity operations
  - Created VatTuModelAbstract with all fields and validation
  - Added proper model documentation and comments
  - Included database indexes for optimization
  - Added support for inventory management features

### Added
- Created tracking document for Color model restructuring
  - Added detailed implementation plan for model updates
  - Defined required fields and structure
  - Outlined repository and service layer requirements
  - Added API endpoint testing checklist

### Changed
- Removed duplicate NhanVienModelSerializer from organization.py to use the one in erp.py
  - Resolves field name mismatch issue with 'ban_hang' field
  - Consolidates serializer definitions in one location
  - Follows the new file organization structure

### Added
- Customer API endpoints (`/api/entities/<entity_slug>/erp/customers/`)
  - Added CustomerModelSerializer
  - Added CustomerViewSet for CRUD operations
  - Added customers endpoint to ERP router
  - Full CRUD support (Create, Read, Update, Delete)
  - Authentication required for all operations

- VuViec Service Layer Implementation
  - Added VuViecService class with full CRUD operations
  - Added VuViecRepository for data access operations
  - Added field validation for dates and decimal values
  - Added business logic validation (e.g. prevent deleting active cases)
  - Added transaction management for data consistency
  - Support for filtering by status (active/inactive)
