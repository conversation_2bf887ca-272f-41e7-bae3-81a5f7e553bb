"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) serializer implementation.  # noqa: E501
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.entity import (  # noqa: F401
    EntityModelSerializer,
)
from django_ledger.api.serializers.tai_san.khai_bao_tang_giam_tscd.khai_bao_thong_tin_tai_san_co_dinh.khai_bao_thong_tin_tai_san_co_dinh import (  # noqa: F401
    KhaiBaoThongTinTaiSanCoDinhSerializer,
)
from django_ledger.models.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import (  # noqa: F401,
    KhaiBaoTamDungKhauHaoTSCDModel,
)


class KhaiBaoTamDungKhauHaoTSCDSerializer(serializers.ModelSerializer):
    """
    Serializer for the KhaiBaoTamDungKhauHaoTSCDModel.

    This serializer handles the conversion between KhaiBaoTamDungKhauHaoTSCDModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).
    """

    # Add nested serializers for foreign key fields
    ma_ts_data = KhaiBaoThongTinTaiSanCoDinhSerializer(
        source='ma_ts', read_only=True
    )

    class Meta:
        model = KhaiBaoTamDungKhauHaoTSCDModel
        fields = [
            'uuid',
            'entity_model',
            'ma_ts',
            'ma_ts_data',
            'ngay_hl_tu',
            'ngay_hl_den',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_ts_data',
            'created',
            'updated',
        ]

    def validate_ma_ts(self, value):
        """
        Validate that ma_ts is provided and not None.
        """
        if value is None:
            raise serializers.ValidationError("Mã tài sản là bắt buộc và không được để trống.")
        return value

    def validate_ngay_hl_tu(self, value):
        """
        Validate that ngay_hl_tu is provided and not None.
        """
        if value is None:
            raise serializers.ValidationError("Ngày hiệu lực từ là bắt buộc và không được để trống.")
        return value

    def validate_ngay_hl_den(self, value):
        """
        Validate that ngay_hl_den is valid when provided and not None.
        """
        if value is None:
            raise serializers.ValidationError("Ngày hiệu lực đến là bắt buộc và không được để trống.")
        return value

    def validate(self, attrs):
        """
        Validate basic date range and unique constraint for ma_ts and ngay_hl_tu combination.
        """
        ma_ts = attrs.get('ma_ts')
        ngay_hl_tu = attrs.get('ngay_hl_tu')
        ngay_hl_den = attrs.get('ngay_hl_den')

        # Both ma_ts and ngay_hl_tu are required
        if not ma_ts:
            raise serializers.ValidationError({
                'ma_ts': "Mã tài sản là bắt buộc và không được để trống."
            })

        if not ngay_hl_tu:
            raise serializers.ValidationError({
                'ngay_hl_tu': "Ngày hiệu lực từ là bắt buộc và không được để trống."
            })

        # Validate date range: ngay_hl_tu <= ngay_hl_den (when ngay_hl_den is provided)
        if ngay_hl_den and ngay_hl_tu > ngay_hl_den:
            raise serializers.ValidationError("Ngày hiệu lực đến phải lớn hơn hoặc bằng ngày hiệu lực từ.")

        # Get entity_slug from context
        entity_slug = self.context.get('entity_slug')
        if entity_slug:
            # Check for duplicate ma_ts and ngay_hl_tu combination
            existing_records = KhaiBaoTamDungKhauHaoTSCDModel.objects.filter(
                entity_model__slug=entity_slug,
                ma_ts=ma_ts,
                ngay_hl_tu=ngay_hl_tu
            )

            # If this is an update operation, exclude the current instance
            if self.instance:
                existing_records = existing_records.exclude(uuid=self.instance.uuid)

            if existing_records.exists():
                raise serializers.ValidationError(
                    "Tài sản này đã có khai báo tạm dừng khấu hao. Vui lòng chọn tài sản khác."
                )

        return attrs
