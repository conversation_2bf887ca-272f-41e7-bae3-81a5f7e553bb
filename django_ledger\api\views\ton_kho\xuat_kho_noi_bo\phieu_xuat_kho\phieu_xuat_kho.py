"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) view implementation.
"""

from rest_framework import permissions, status, viewsets  # noqa: F401
from rest_framework.response import Response  # noqa: F401

from django_ledger.api.serializers.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401
    PhieuXuatKhoSerializer,
)
from django_ledger.api.views.common import ERPPagination  # noqa: F401,
from django_ledger.api.viewsets import EntityRelatedViewSet  # noqa: F401,
from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoModel,
)
from django_ledger.services.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoService,
)


class PhieuXuatKhoViewSet(EntityRelatedViewSet):
    """
    ViewSet for PhieuXuatKhoModel.
    Handles API requests for the model.
    """

    queryset = PhieuXuatKhoModel.objects.all()
    serializer_class = PhieuXuatKhoSerializer  # noqa: F811
    lookup_field = 'pk'
    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination  # noqa: F811

    def __init__(self, **kwargs):  # noqa: C901
        super().__init__(**kwargs)
        self.service = PhieuXuatKhoService()

    def get_queryset(self):  # noqa: C901
        """
        Get QuerySet of PhieuXuatKhoModel instances for the entity

        Returns
        -------
        QuerySet
            QuerySet of PhieuXuatKhoModel instances for the entity
        """
        entity_slug = self.kwargs.get('entity_slug')
        return (
            self.queryset.for_entity(entity_slug=entity_slug)
            .prefetch_related(
                'chi_tiet_phieu_xuat_kho',
                'chi_tiet_phieu_xuat_kho__ma_vt',
                'chi_tiet_phieu_xuat_kho__dvt',
                'chi_tiet_phieu_xuat_kho__ma_kho',
                'chi_tiet_phieu_xuat_kho__ma_lo',
                'chi_tiet_phieu_xuat_kho__ma_vi_tri',
                'chi_tiet_phieu_xuat_kho__tk_vt',
                'chi_tiet_phieu_xuat_kho__ma_nx',
                'chi_tiet_phieu_xuat_kho__tk_du',
            )
            .select_related(
                'entity_model',
                'ma_kh',
                'chung_tu_item',
                'chung_tu_item__ma_nk',
                'chung_tu',
                'ma_nt',
                'unit_id',
            )
        )

    def list(self, request, *args, **kwargs):  # noqa: C901
        """
        Lists PhieuXuatKhoModel instances for a specific entity.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            A response containing the serialized PhieuXuatKhoModel instances.
        """
        try:
            # Get queryset using the standard get_queryset method
            queryset = self.get_queryset()
            # Apply any filters if needed
            # (Add filter logic here if required)

            # Use the standard paginate_queryset method provided by DRF
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'message': str(e), 'error': 'Error listing objects'},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def retrieve(self, request, entity_slug=None, pk=None):  # noqa: F811,
        """
        Retrieves a PhieuXuatKhoModel instance.

        Parameters
        ----------
        request : Request
            The request object.
        entity_slug : str
            The entity slug.
        pk : str
            The UUID of the PhieuXuatKhoModel to retrieve.
        """
        try:
            # Use get_queryset() to ensure proper prefetch_related for chi_tiet
            queryset = self.get_queryset()
            instance = queryset.filter(uuid=pk).first()

            if not instance:
                return Response(
                    {'detail': 'PhieuXuatKho not found.'},
                    status=status.HTTP_404_NOT_FOUND,
                )

            serializer = self.get_serializer(instance)
            return Response(serializer.data)
        except Exception as e:
            return Response({'message': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def create(self, request, *args, **kwargs):  # noqa: C901
        """
        Creates a new PhieuXuatKhoModel instance.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            A response containing the serialized created PhieuXuatKhoModel instance.
        """
        try:
            entity_slug = self.kwargs.get('entity_slug')
            serializer = self.get_serializer(data=request.data)
            if serializer.is_valid():
                instance = self.service.create(
                    entity_slug=entity_slug,
                    data=serializer.validated_data,
                )
                # Refetch instance with proper prefetch_related for chi_tiet
                instance_with_chi_tiet = self.get_queryset().filter(uuid=instance.uuid).first()
                return Response(
                    self.get_serializer(instance_with_chi_tiet).data, status=status.HTTP_201_CREATED
                )
            return Response(
                {'message': serializer.errors, 'error': 'Validation error'},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {'message': str(e), 'error': 'Error creating object'},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def update(self, request, *args, **kwargs):  # noqa: C901
        """
        Updates an existing PhieuXuatKhoModel instance.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            A response containing the serialized updated PhieuXuatKhoModel instance.
        """
        try:
            entity_slug = self.kwargs.get('entity_slug')
            pk = self.kwargs.get('pk')
            # Get the instance using the standard get_object method
            instance = self.get_object()
            # Update the instance
            serializer = self.get_serializer(instance, data=request.data, partial=True)
            if serializer.is_valid():
                updated_instance = self.service.update(
                    entity_slug=entity_slug,
                    uuid=pk,
                    data=serializer.validated_data,
                )

                if not updated_instance:
                    return Response(
                        {'detail': 'Failed to update PhieuXuatKho.'},
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Refetch instance with proper prefetch_related for chi_tiet
                instance_with_chi_tiet = self.get_queryset().filter(uuid=updated_instance.uuid).first()
                return Response(self.get_serializer(instance_with_chi_tiet).data)
            return Response(
                {'message': serializer.errors, 'error': 'Validation error'},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            return Response(
                {'message': str(e), 'error': 'Error updating object'},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def destroy(self, request, *args, **kwargs):  # noqa: C901
        """
        Deletes a PhieuXuatKhoModel instance.

        Parameters
        ----------
        request : Request
            The request object.

        Returns
        -------
        Response
            A response indicating the success or failure of the deletion.
        """
        try:
            entity_slug = self.kwargs.get('entity_slug')
            pk = self.kwargs.get('uuid')
            success = self.service.delete(entity_slug=entity_slug, uuid=pk)
            if success:
                return Response(status=status.HTTP_204_NO_CONTENT)
            return Response(
                {'detail': 'PhieuXuatKho not found.'},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {'message': str(e), 'error': 'Error deleting object'},
                status=status.HTTP_400_BAD_REQUEST,
            )
