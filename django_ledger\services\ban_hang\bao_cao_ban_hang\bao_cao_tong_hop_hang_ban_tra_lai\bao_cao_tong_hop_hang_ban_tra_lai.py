"""
Sales Return Summary Report Service (Bao Cao Tong Hop Hang Ban Tra Lai).

This service provides functionality for generating sales return summary reports
following the established codebase patterns.
"""

from typing import Dict, Any, List, Optional
from django.core.exceptions import ValidationError
import logging

from django_ledger.models.entity import EntityModel
from django_ledger.services.transaction_base.transaction_report_service import BaseTransactionReportService


class BaoCaoTongHopHangBanTraLaiService(BaseTransactionReportService):
    """
    Sales Return Summary Report Service.
    
    This service generates comprehensive sales return summary reports showing:
    - Return quantities and amounts by customer
    - Material information and pricing
    - Sales representative details
    - Warehouse and location information
    
    Inherits from BaseTransactionReportService for consistent transaction-based reporting.
    """

    def __init__(self):
        """Initialize the service."""
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self._phieu_nhap_service = None
        self.logger.info("BaoCaoTongHopHangBanTraLaiService initialized")

    @property
    def phieu_nhap_service(self):
        """Lazy initialization of PhieuNhapHangBanTraLaiService."""
        if self._phieu_nhap_service is None:
            from django_ledger.services.ban_hang.hoa_don_dieu_chinh_tra_lai.phieu_nhap_hang_ban_tra_lai import (
                PhieuNhapHangBanTraLaiService,
            )
            self._phieu_nhap_service = PhieuNhapHangBanTraLaiService()
        return self._phieu_nhap_service

    def get_report(
        self,
        entity_slug: str,
        filters: Dict[str, Any],
        user=None
    ) -> List[Dict[str, Any]]:
        """
        Generate sales return summary report.

        Parameters
        ----------
        entity_slug : str
            Entity slug for multi-tenant filtering
        filters : Dict[str, Any]
            Filter parameters for the report
        user : User, optional
            User requesting the report (for permissions), by default None

        Returns
        -------
        List[Dict[str, Any]]
            List of report data dictionaries

        Raises
        ------
        ValidationError
            If validation fails
        ValueError
            If entity not found or invalid parameters
        """
        try:
            self.logger.info(f"Generating sales return summary report for entity: {entity_slug}")

            entity = self._validate_entity(entity_slug, user)
            validated_filters = self._validate_filters(filters)
            report_data = self._execute_report_query(entity.uuid, validated_filters)
            processed_results = self._process_results(report_data, validated_filters)

            self.logger.info(f"Generated {len(processed_results)} records for sales return summary report")
            return processed_results

        except Exception as e:
            self.logger.error(f"Error generating sales return summary report: {str(e)}")
            raise

    def _validate_entity(self, entity_slug: str, user=None) -> EntityModel:
        """
        Validate entity using ORM.

        Parameters
        ----------
        entity_slug : str
            Entity slug
        user : User, optional
            User for permission checking

        Returns
        -------
        EntityModel
            Validated entity instance

        Raises
        ------
        ValueError
            If entity not found or access denied
        """
        try:
            entity = EntityModel.objects.get(slug=entity_slug)
            return entity

        except EntityModel.DoesNotExist:
            self.logger.error(f"Entity not found: {entity_slug}")
            raise ValueError(f"Entity not found: {entity_slug}")

    def _validate_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean filter parameters.

        Parameters
        ----------
        filters : Dict[str, Any]
            Raw filter parameters

        Returns
        -------
        Dict[str, Any]
            Validated and cleaned filters
        """
        validated_filters = {}
        
        for key, value in filters.items():
            if value is not None and value != '':
                validated_filters[key] = value

        return validated_filters

    def _execute_report_query(
        self,
        entity_uuid: str,
        filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Execute the main report query using raw SQL.

        Builds the query and uses service-to-service communication following CCDC allocation pattern.

        Parameters
        ----------
        entity_uuid : str
            Entity UUID for filtering
        filters : Dict[str, Any]
            Validated filter parameters

        Returns
        -------
        List[Dict[str, Any]]
            Raw query results
        """

        where_conditions = ["entity_model_id = %s"]
        query_params = [str(entity_uuid).replace('-', '')]  # Convert UUID to string without dashes for SQLite

        if filters.get('ma_kh'):
            where_conditions.append("ma_kh = %s")
            query_params.append(str(filters['ma_kh']).replace('-', ''))

        if filters.get('ma_nvbh'):
            where_conditions.append("ma_nvbh = %s")
            query_params.append(str(filters['ma_nvbh']).replace('-', ''))

        if filters.get('ngay_ct1'):
            where_conditions.append("ngay_ct >= %s")
            query_params.append(filters['ngay_ct1'])

        if filters.get('ngay_ct2'):
            where_conditions.append("ngay_ct <= %s")
            query_params.append(filters['ngay_ct2'])

        if filters.get('so_ct1'):
            where_conditions.append("so_ct >= %s")
            query_params.append(filters['so_ct1'])

        if filters.get('so_ct2'):
            where_conditions.append("so_ct <= %s")
            query_params.append(filters['so_ct2'])

        if filters.get('ma_unit'):
            where_conditions.append("unit_id_id = %s")
            query_params.append(filters['ma_unit'])

        if filters.get('dien_giai'):
            where_conditions.append("dien_giai LIKE %s")
            query_params.append(f"%{filters['dien_giai']}%")

        customer_conditions = []
        customer_params = []
        customer_has_filters = False

        if filters.get('nh_kh1'):
            customer_conditions.append("nh_kh1 = %s")
            customer_params.append(filters['nh_kh1'])
            customer_has_filters = True

        if filters.get('nh_kh2'):
            customer_conditions.append("nh_kh2 = %s")
            customer_params.append(filters['nh_kh2'])
            customer_has_filters = True

        if filters.get('nh_kh3'):
            customer_conditions.append("nh_kh3 = %s")
            customer_params.append(filters['nh_kh3'])
            customer_has_filters = True

        if filters.get('rg_code'):
            customer_conditions.append("rg_code = %s")
            customer_params.append(filters['rg_code'])
            customer_has_filters = True

        if customer_has_filters:
            customer_subquery = f"""(
                SELECT * FROM django_ledger_customermodel
                WHERE {' AND '.join(customer_conditions)}
            ) kh"""
            query_params.extend(customer_params)
        else:
            customer_subquery = "django_ledger_customermodel kh"

        detail_conditions = []
        detail_params = []
        detail_has_filters = False

        if filters.get('ma_vt'):
            detail_conditions.append("ct.ma_vt_id = %s")
            detail_params.append(str(filters['ma_vt']).replace('-', ''))
            detail_has_filters = True

        if filters.get('ma_kho'):
            detail_conditions.append("ct.ma_kho_id = %s")
            detail_params.append(str(filters['ma_kho']).replace('-', ''))
            detail_has_filters = True

        if filters.get('tk_gv'):
            detail_conditions.append("ct.tk_gv = %s")
            detail_params.append(filters['tk_gv'])
            detail_has_filters = True

        if filters.get('ma_lo'):
            detail_conditions.append("ct.ma_lo = %s")
            detail_params.append(filters['ma_lo'])
            detail_has_filters = True

        if filters.get('tk_vt'):
            detail_conditions.append("ct.tk_vt = %s")
            detail_params.append(filters['tk_vt'])
            detail_has_filters = True

        if filters.get('tk_dt'):
            detail_conditions.append("ct.tk_dt = %s")
            detail_params.append(filters['tk_dt'])
            detail_has_filters = True

        if filters.get('ma_lvt'):
            detail_conditions.append("vt.ma_lvt = %s")
            detail_params.append(filters['ma_lvt'])
            detail_has_filters = True

        if filters.get('ton_kho_yn'):
            detail_conditions.append("vt.ton_kho_yn = %s")
            detail_params.append(filters['ton_kho_yn'])
            detail_has_filters = True

        if filters.get('nh_vt1'):
            detail_conditions.append("vt.nh_vt1 = %s")
            detail_params.append(filters['nh_vt1'])
            detail_has_filters = True

        if filters.get('nh_vt2'):
            detail_conditions.append("vt.nh_vt2 = %s")
            detail_params.append(filters['nh_vt2'])
            detail_has_filters = True

        if filters.get('nh_vt3'):
            detail_conditions.append("vt.nh_vt3 = %s")
            detail_params.append(filters['nh_vt3'])
            detail_has_filters = True

        if filters.get('ma_vi_tri'):
            detail_conditions.append("vt.ma_vi_tri = %s")
            detail_params.append(filters['ma_vi_tri'])
            detail_has_filters = True

        mau_bc = filters.get('mau_bc')

        if detail_has_filters:
            detail_subquery = f"""(
                SELECT
                    ct.uuid,
                    ct.phieu_nhap_hang_ban_tra_lai_id,
                    ct.ma_vt_id,
                    ct.ma_kho_id,
                    ct.so_luong,
                    ct.he_so,
                    ct.gia,
                    ct.tien,
                    ct.gia_nt2,
                    ct.tien2,
                    ct.thue,
                    ct.tl_ck as pt,
                    ct.ck as tien_lai,
                    ct.gia_nt1,
                    ct.tien_nt,
                    ct.tien_nt2,
                    COALESCE(vt.ma_vt, '') as ma_vt,
                    COALESCE(vt.ten_vt, '') as ten_vt,
                    COALESCE(dvt.ten_dvt, '') as dvt
                FROM chi_tiet_phieu_nhap_hang_ban_tra_lai ct
                LEFT JOIN dmvt vt ON ct.ma_vt_id = vt.uuid
                LEFT JOIN dmdvt dvt ON vt.dvt_id = dvt.uuid
                WHERE {' AND '.join(detail_conditions)}
            ) ct"""
            query_params.extend(detail_params)
        else:
            detail_subquery = """(
                SELECT
                    ct.uuid,
                    ct.phieu_nhap_hang_ban_tra_lai_id,
                    ct.ma_vt_id,
                    ct.ma_kho_id,
                    ct.so_luong,
                    ct.he_so,
                    ct.gia,
                    ct.tien,
                    ct.gia_nt2,
                    ct.tien2,
                    ct.thue,
                    ct.tl_ck as pt,
                    ct.ck as tien_lai,
                    ct.gia_nt1,
                    ct.tien_nt,
                    ct.tien_nt2,
                    COALESCE(vt.ma_vt, '') as ma_vt,
                    COALESCE(vt.ten_vt, '') as ten_vt,
                    COALESCE(dvt.ten_dvt, '') as dvt
                FROM chi_tiet_phieu_nhap_hang_ban_tra_lai ct
                LEFT JOIN dmvt vt ON ct.ma_vt_id = vt.uuid
                LEFT JOIN dmdvt dvt ON vt.dvt_id = dvt.uuid
            ) ct"""

        if mau_bc == "10":
            select_fields = """
                COALESCE(ct.ma_vt, '') as ma_vt,
                COALESCE(ct.ten_vt, '') as ten_vt,
                COALESCE(ct.dvt, '') as dvt,
                COALESCE(ct.so_luong, 0) as sl_nhap
            """
        elif mau_bc == "20":
            select_fields = """
                COALESCE(ct.ma_vt, '') as ma_vt,
                COALESCE(ct.so_luong, 0) as sl_nhap,
                COALESCE(ct.gia, 0) as gia,
                COALESCE(ct.tien, 0) as tien_nhap,
                COALESCE(ct.gia_nt2, 0) as gia2,
                COALESCE(ct.gia_nt2 * ct.so_luong, 0) as tien2,
                COALESCE(ct.thue, 0) as thue,
                COALESCE(ct.ten_vt, '') as ten_vt,
                COALESCE(ct.dvt, '') as dvt,
                COALESCE(ct.pt, 0) as pt,
                COALESCE(ct.tien_lai, 0) as tien_lai
            """
        elif mau_bc == "30":
            select_fields = """
                COALESCE(ct.ma_vt, '') as ma_vt,
                COALESCE(ct.so_luong, 0) as sl_nhap,
                COALESCE(ct.gia, 0) as gia,
                COALESCE(ct.tien, 0) as tien_nhap,
                COALESCE(ct.gia_nt2, 0) as gia2,
                COALESCE(ct.gia_nt2 * ct.so_luong, 0) as tien2,
                COALESCE(ct.thue, 0) as thue,
                COALESCE(ct.ten_vt, '') as ten_vt,
                COALESCE(ct.dvt, '') as dvt,
                COALESCE(ct.pt, 0) as pt,
                COALESCE(ct.tien_lai, 0) as tien_lai,
                COALESCE(ct.gia_nt1, 0) as gia_nt1,
                COALESCE(ct.tien_nt, 0) as tien_nt1,
                COALESCE(ct.tien_nt2, 0) as tien_nt2,
                COALESCE(nt.ma_nt, '') as ma_nt
            """

        base_query = f"""
        SELECT {select_fields}
        FROM (
            SELECT uuid, ngay_ct, so_ct, ma_kh, ma_nt_id, entity_model_id
            FROM phieu_nhap_hang_ban_tra_lai
            WHERE {' AND '.join(where_conditions)}
        ) pn
        INNER JOIN {detail_subquery} ON pn.uuid = ct.phieu_nhap_hang_ban_tra_lai_id
        LEFT JOIN {customer_subquery} ON pn.ma_kh = kh.customer_code"""

        if mau_bc == "30":
            base_query += """
        LEFT JOIN ngoai_te nt ON pn.ma_nt_id = nt.uuid"""
        base_query += " ORDER BY pn.ngay_ct, pn.so_ct"

        try:
            results = self.phieu_nhap_service.execute_raw_query(base_query, query_params)
            return results

        except Exception as e:
            raise

    def _process_results(
        self,
        raw_results: List[Dict[str, Any]],
        filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process and transform raw query results.

        Parameters
        ----------
        raw_results : List[Dict[str, Any]]
            Raw query results
        filters : Dict[str, Any]
            Applied filters

        Returns
        -------
        List[Dict[str, Any]]
            Processed results ready for serialization
        """
        processed_results = []
        mau_bc = filters.get('mau_bc')

        for row in raw_results:
            if mau_bc == "10":
                processed_row = {
                    'ma_vt': row.get('ma_vt'),
                    'ten_vt': row.get('ten_vt', ''),
                    'dvt': row.get('dvt', ''),
                    'sl_nhap': row.get('sl_nhap', 0),
                }
            elif mau_bc == "20":
                processed_row = {
                    'ma_vt': row.get('ma_vt'),
                    'sl_nhap': row.get('sl_nhap', 0),
                    'gia': row.get('gia', 0),
                    'tien_nhap': row.get('tien_nhap', 0),
                    'gia2': row.get('gia2', 0),
                    'tien2': row.get('tien2', 0),
                    'thue': row.get('thue', 0),
                    'ten_vt': row.get('ten_vt', ''),
                    'dvt': row.get('dvt', ''),
                    'pt': row.get('pt', 0),
                    'tien_lai': row.get('tien_lai', 0),
                }
            elif mau_bc == "30":
                processed_row = {
                    'ma_vt': row.get('ma_vt'),
                    'sl_nhap': row.get('sl_nhap', 0),
                    'gia': row.get('gia', 0),
                    'tien_nhap': row.get('tien_nhap', 0),
                    'gia2': row.get('gia2', 0),
                    'tien2': row.get('tien2', 0),
                    'thue': row.get('thue', 0),
                    'ten_vt': row.get('ten_vt', ''),
                    'dvt': row.get('dvt', ''),
                    'pt': row.get('pt', 0),
                    'tien_lai': row.get('tien_lai', 0),
                    'ma_nt': row.get('ma_nt', ''),
                    'gia_nt1': row.get('gia_nt1', 0),
                    'tien_nt1': row.get('tien_nt1', 0),
                    'tien_nt2': row.get('tien_nt2', 0),
                }

            processed_results.append(processed_row)

        return processed_results
