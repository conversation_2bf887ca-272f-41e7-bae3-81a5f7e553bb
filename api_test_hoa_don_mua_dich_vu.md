# HoaDonMuaDichVu API Testing Guide

## Base URL

```
http://127.0.0.1:8003
```

## Authentication

```
Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389
```

## Entity Information

- **Entity Slug**: `tutimi-dnus2xnc`
- **Entity UUID**: `b0bb20d59da44fd8884992b5faa6a4cc`

## 1. HoaDonMuaDichVu Main API

### 1.1 GET - List All Purchase Service Invoices

```bash
curl -X GET \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
  -H 'Content-Type: application/json'
```

### 1.2 POST - Create New Purchase Service Invoice

```bash
curl -X POST \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
  -H 'Content-Type: application/json' \
  -d '{
    "ma_gd": "GD001",
    "pc_tao_yn": "N",
    "ma_httt": "TM",
    "loai_ck": "PT",
    "ck_tl_nt": 0.0,
    "ma_kh": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ma_so_thue": "0123456789",
    "du_cn_thu": 0.0,
    "dia_chi": "123 Main Street",
    "ong_ba": "Nguyen Van A",
    "ma_nvmh": "37899ca8-dfa9-4d85-9317-19fce573a518",
    "e_mail": "<EMAIL>",
    "tk": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ma_tt": "9bee56e5-9a5b-4eb3-a099-05e95203c4aa",
    "dien_giai": "Hoa don mua dich vu test",
    "id": "ID001",
    "unit_id": "7043926df26f463299c344f2c13ac6e4",
    "i_so_ct": "001",
    "ma_nk": "fac3ae01-26a0-4b01-9020-979e903c6e7c",
    "so_ct": "HD001",
    "ngay_ct": "2025-01-02",
    "ngay_lct": "2025-01-02",
    "so_ct0": "HD001",
    "so_ct2": "HD001-2",
    "ngay_ct0": "2025-01-02",
    "ma_nt": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ty_gia": 1.0,
    "status": "1",
    "transfer_yn": "N",
    "ma_ngv": "NGV001",
    "pc_ngay_ct": "2025-01-02",
    "pc_ma_ct": "PC001",
    "pc_ten_ct": "Phieu chi test",
    "pc_ma_nk": "PC",
    "pc_ten_nk": "Phieu chi",
    "pc_tknh": "1111",
    "pc_tk": "1112",
    "pc_ten_tk": "Tai khoan ngan hang",
    "pc_t_tt_nt": 1000000.0,
    "so_ct_tt": "TT001",
    "t_thue_nt": 100000.0,
    "t_thue": 100000.0,
    "t_tien_nt": 1000000.0,
    "t_tien": 1000000.0,
    "t_ck_nt_ex": 0.0,
    "t_ck_ex": 0.0,
    "t_ck_nt": 0.0,
    "t_ck": 0.0,
    "t_tt_nt": 1100000.0,
    "t_tt": 1100000.0,
    "chi_tiet": [
      {
        "line": 1,
        "ma_dv": "DV001",
        "x_new_item": "N",
        "tk_vt": "6411",
        "ten_tk": "Chi phi dich vu",
        "dien_giai": "Dich vu tu van",
        "ma_lts": "LTS001",
        "dvt": "Gio",
        "so_luong": 10.0,
        "gia_nt": 100000.0,
        "tien_nt": 1000000.0,
        "tien_tck_nt": 1000000.0,
        "tl_ck": 0.0,
        "ck_nt": 0.0,
        "gia": 100000.0,
        "tien": 1000000.0,
        "tien_tck": 1000000.0,
        "ck": 0.0,
        "ma_thue": "VAT10",
        "thue_suat": 10.0,
        "thue_nt": 100000.0,
        "thue": 100000.0,
        "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
        "ma_vv": "89794195-b75e-4ef4-bb09-6d480ff9df0c",
        "ma_hd": "13c29a44-7aa8-44e6-9ee8-4cedc7d72ef1",
        "ma_dtt": "DTT001",
        "ma_ku": "26b41e71-8c6f-4637-9ce7-80fbb448ec18",
        "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d",
        "ma_sp": "SP001",
        "ma_lsx": "LSX001",
        "ma_cp0": "f3ff213c-03de-47e2-b56b-9ac20bdc724e",
        "id_tb": "TB001",
        "line_tb": 1
      }
    ],
    "thue": [
      {
        "line": 1,
        "i_so_ct": "001",
        "line_ct": 1,
        "ma_ts": "TS001",
        "ma_lts": "LTS001",
        "ngay_mua": "2025-01-02",
        "ngay_kh0": "2025-01-02",
        "so_ky_kh": 60,
        "ngay_kh_kt": "2030-01-02",
        "nguyen_gia_nt": 1000000.0,
        "gt_da_kh_nt": 0.0,
        "gt_cl_nt": 1000000.0,
        "gt_kh_ky_nt": 16666.67,
        "nguyen_gia": 1000000.0,
        "gt_da_kh": 0.0,
        "gt_cl": 1000000.0,
        "gt_kh_ky": 16666.67,
        "so_hieu_ts": "TS001-SN",
        "tk_ts": "2111",
        "tk_kh": "2118",
        "tk_cp": "6427",
        "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
        "ma_vv": "89794195-b75e-4ef4-bb09-6d480ff9df0c",
        "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d"
      }
    ]
  }'
```

### 1.3 GET - Get Specific Purchase Service Invoice

```bash
curl -X GET \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
  -H 'Content-Type: application/json'
```

### 1.4 PUT - Update Purchase Service Invoice

```bash
curl -X PUT \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
  -H 'Content-Type: application/json' \
  -d '{
    "ma_gd": "GD001-UPDATED",
    "dien_giai": "Hoa don mua dich vu updated",
    "status": "2",
    "t_tien": 1200000.0,
    "t_tt": 1320000.0
  }'
```

### 1.5 PATCH - Partial Update Purchase Service Invoice

```bash
curl -X PATCH \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "status": "3",
    "dien_giai": "Hoa don da duyet"
  }'
```

### 1.6 DELETE - Delete Purchase Service Invoice

```bash
curl -X DELETE \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## 2. ChiTietHoaDonMuaDichVu API

### 2.1 GET - List Invoice Details

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json'
```

### 2.2 POST - Create Invoice Detail

```bash
curl -X POST \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "line": 2,
    "ma_dv": "DV002",
    "x_new_item": "N",
    "tk_vt": "6412",
    "ten_tk": "Chi phi dich vu khac",
    "dien_giai": "Dich vu bao tri",
    "ma_lts": "LTS002",
    "dvt": "Lan",
    "so_luong": 1.0,
    "gia_nt": 500000.0,
    "tien_nt": 500000.0,
    "tien_tck_nt": 500000.0,
    "tl_ck": 0.0,
    "ck_nt": 0.0,
    "gia": 500000.0,
    "tien": 500000.0,
    "tien_tck": 500000.0,
    "ck": 0.0,
    "ma_thue": "VAT10",
    "thue_suat": 10.0,
    "thue_nt": 50000.0,
    "thue": 50000.0,
    "ma_bp": "department-uuid-here",
    "ma_vv": "case-uuid-here",
    "ma_hd": "contract-uuid-here",
    "ma_dtt": "DTT001",
    "ma_ku": "area-uuid-here",
    "ma_phi": "fee-uuid-here",
    "ma_sp": "SP002",
    "ma_lsx": "LSX002",
    "ma_cp0": "cost-center-uuid-here",
    "id_tb": "TB002",
    "line_tb": 2
  }'
```

### 2.3 GET - Get Specific Invoice Detail

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/{detail_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json'
```

### 2.4 PUT - Update Invoice Detail

```bash
curl -X PUT \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/{detail_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "line": 2,
    "ma_dv": "DV002-UPDATED",
    "dien_giai": "Dich vu bao tri updated",
    "so_luong": 2.0,
    "gia_nt": 600000.0,
    "tien_nt": 1200000.0,
    "tien": 1200000.0
  }'
```

### 2.5 PATCH - Partial Update Invoice Detail

```bash
curl -X PATCH \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/{detail_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "so_luong": 3.0,
    "tien_nt": 1500000.0,
    "tien": 1500000.0
  }'
```

### 2.6 DELETE - Delete Invoice Detail

```bash
curl -X DELETE \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/chi-tiet/{detail_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## 3. ThueHoaDonMuaDichVu API

### 3.1 GET - List Invoice Tax Records

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json'
```

### 3.2 POST - Create Invoice Tax Record

```bash
curl -X POST \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "line": 2,
    "i_so_ct": "002",
    "line_ct": 2,
    "ma_ts": "TS002",
    "ma_lts": "LTS002",
    "ngay_mua": "2025-01-02",
    "ngay_kh0": "2025-01-02",
    "so_ky_kh": 36,
    "ngay_kh_kt": "2028-01-02",
    "nguyen_gia_nt": 500000.0,
    "gt_da_kh_nt": 0.0,
    "gt_cl_nt": 500000.0,
    "gt_kh_ky_nt": 13888.89,
    "nguyen_gia": 500000.0,
    "gt_da_kh": 0.0,
    "gt_cl": 500000.0,
    "gt_kh_ky": 13888.89,
    "so_hieu_ts": "TS002-SN",
    "tk_ts": "2112",
    "tk_kh": "2119",
    "tk_cp": "6428",
    "ma_bp": "department-uuid-here",
    "ma_vv": "case-uuid-here",
    "ma_phi": "fee-uuid-here"
  }'
```

### 3.3 GET - Get Specific Invoice Tax Record

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/{tax_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json'
```

### 3.4 PUT - Update Invoice Tax Record

```bash
curl -X PUT \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/{tax_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "line": 2,
    "ma_ts": "TS002-UPDATED",
    "nguyen_gia_nt": 600000.0,
    "nguyen_gia": 600000.0,
    "gt_cl_nt": 600000.0,
    "gt_cl": 600000.0,
    "gt_kh_ky_nt": 16666.67,
    "gt_kh_ky": 16666.67
  }'
```

### 3.5 PATCH - Partial Update Invoice Tax Record

```bash
curl -X PATCH \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/{tax_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE' \
  -H 'Content-Type: application/json' \
  -d '{
    "nguyen_gia": 700000.0,
    "gt_cl": 700000.0
  }'
```

### 3.6 DELETE - Delete Invoice Tax Record

```bash
curl -X DELETE \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/thue/{tax_uuid}/' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## 4. Query Parameters & Filtering

### 4.1 Pagination

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?page=1&page_size=10' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

### 4.2 Filtering

```bash
# Filter by status
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?status=1' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'

# Filter by customer
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?ma_kh={vendor_uuid}' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'

# Filter by date
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?ngay_ct=2025-01-02' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

### 4.3 Search

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?search=HD001' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

### 4.4 Ordering

```bash
curl -X GET \
  'http://localhost:8003/api/v1/entities/{entity_slug}/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/?ordering=-ngay_ct' \
  -H 'Authorization: Bearer YOUR_TOKEN_HERE'
```

## 5. Testing Notes

### Required UUIDs to Replace:

- `{entity_slug}`: Your entity slug (e.g., "my-company")
- `{invoice_uuid}`: UUID of the created invoice
- `{detail_uuid}`: UUID of the invoice detail
- `{tax_uuid}`: UUID of the tax record
- `vendor-uuid-here`: UUID of vendor
- `staff-uuid-here`: UUID of staff
- `account-uuid-here`: UUID of account
- `payment-terms-uuid-here`: UUID of payment terms
- `unit-uuid-here`: UUID of organization unit
- `document-series-uuid-here`: UUID of document series
- `currency-uuid-here`: UUID of currency
- `department-uuid-here`: UUID of department
- `case-uuid-here`: UUID of case/matter
- `contract-uuid-here`: UUID of contract
- `area-uuid-here`: UUID of area
- `fee-uuid-here`: UUID of fee
- `cost-center-uuid-here`: UUID of cost center

### Expected Response Status Codes:

- GET: 200 OK
- POST: 201 Created
- PUT: 200 OK
- PATCH: 200 OK
- DELETE: 204 No Content

### Accounting Integration:

When creating/updating invoices, the system will automatically:

1. Create DICHVU journal entries for service expenses
2. Create THUE journal entries for asset depreciation
3. Handle conditional accounting based on status field
4. Maintain transaction consistency with rollback on errors

### Error Handling:

- 400 Bad Request: Invalid data
- 401 Unauthorized: Missing/invalid token
- 404 Not Found: Resource not found
- 500 Internal Server Error: Server error (check accounting integration)

### Sample Test Sequence:

1. Start server: `python manage.py runserver 8003`
2. Create invoice using POST request (1.2)
3. Get invoice UUID from response
4. Test GET specific invoice (1.3)
5. Add invoice details using POST (2.2)
6. Add tax records using POST (3.2)
7. Update invoice status using PATCH (1.5)
8. Verify accounting entries were created automatically
