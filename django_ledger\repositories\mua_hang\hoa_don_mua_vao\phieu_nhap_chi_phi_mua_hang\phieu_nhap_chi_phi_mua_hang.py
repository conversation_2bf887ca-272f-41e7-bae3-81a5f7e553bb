"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the PhieuNhapChiPhiMuaHangRepository, which handles database operations  # noqa: E501
for the PhieuNhapChiPhiMuaHangModel.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401,
    PhieuNhapChiPhiMuaHangModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuNhapChiPhiMuaHangRepository(BaseRepository):
    """
    Repository class for PhieuNhapChiPhiMuaHangModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=PhieuNhapChiPhiMuaHangModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuNhapChiPhiMuaHangModel.
        """
        return self.model_class.objects.all()

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Retrieves a PhieuNhapChiPhiMuaHangModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to retrieve.

        Returns
        -------
        Optional[PhieuNhapChiPhiMuaHangModel]
            The PhieuNhapChiPhiMuaHangModel with the given UUID, or None if not found.
        """
        try:
            return self.model_class.objects.for_entity(entity_slug=entity_slug).get(
                uuid=uuid
            )
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuNhapChiPhiMuaHangModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.for_entity(entity_slug=entity_slug).filter(
            **kwargs
        )

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        Creates a new PhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModel
            The created PhieuNhapChiPhiMuaHangModel instance.
        """
        from django_ledger.models import EntityModel  # noqa: F401,

        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Extract ChungTu fields from original data BEFORE UUID conversion
        chung_tu_fields = {}
        chung_tu_field_names = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']
        data_copy = data.copy()

        for field_name in chung_tu_field_names:
            if field_name in data_copy:
                chung_tu_fields[field_name] = data_copy.pop(field_name)

        # Convert UUIDs to model instances for ChungTu fields
        self.convert_chung_tu_uuids_to_model_instances(chung_tu_fields)

        # Convert UUID strings to model instances for remaining data
        processed_data = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()

        return instance

    def create_with_entity(
        self, entity_model, data: Dict[str, Any]
    ) -> PhieuNhapChiPhiMuaHangModel:  # noqa: C901
        """
        Creates a new PhieuNhapChiPhiMuaHangModel instance with entity_model.

        Parameters
        ----------
        entity_model : EntityModel
            The entity model.
        data : Dict[str, Any]
            The data for the new PhieuNhapChiPhiMuaHangModel.

        Returns
        -------
        PhieuNhapChiPhiMuaHangModel
            The created PhieuNhapChiPhiMuaHangModel instance.
        """
        # Remove related data from main model data - they will be handled separately
        main_data = data.copy()
        main_data.pop('chi_tiet_phieu_nhaps', None)
        main_data.pop('chi_phi_phieu_nhaps', None)
        main_data.pop('chi_phi_chi_tiet_phieu_nhaps', None)
        main_data.pop('thue_phieu_nhaps', None)

        # Extract ChungTu fields from original data BEFORE UUID conversion
        chung_tu_fields = {}
        chung_tu_field_names = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']
        data_copy = main_data.copy()

        for field_name in chung_tu_field_names:
            if field_name in data_copy:
                chung_tu_fields[field_name] = data_copy.pop(field_name)

        # Convert UUIDs to model instances for ChungTu fields
        self.convert_chung_tu_uuids_to_model_instances(chung_tu_fields)

        # Convert UUID strings to model instances for remaining data
        processed_data = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()

        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuNhapChiPhiMuaHangModel]:  # noqa: C901
        """
        Updates an existing PhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to update.
        data : Dict[str, Any]
            The data to update the PhieuNhapChiPhiMuaHangModel with.

        Returns
        -------
        Optional[PhieuNhapChiPhiMuaHangModel]
            The updated PhieuNhapChiPhiMuaHangModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Convert UUID strings to model instances before updating
            self.convert_chung_tu_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            instance.save()
            return instance
        return None

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuNhapChiPhiMuaHangModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuNhapChiPhiMuaHangModel to delete.

        Returns
        -------
        bool
            True if the PhieuNhapChiPhiMuaHangModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def get_by_supplier(
        self, supplier_id: str, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances for a specific supplier.

        Parameters
        ----------
        supplier_id : str
            The supplier UUID.
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.filter(
            entity_model__slug=entity_slug, ma_kh__uuid=supplier_id
        ).order_by('-created')

    def get_by_status(self, status: str, entity_slug: str) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances with a specific status.

        Parameters
        ----------
        status : str
            The status to filter by.
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.filter(
            entity_model__slug=entity_slug, status=status
        ).order_by('-created')

    def get_by_date_range(
        self, start_date: str, end_date: str, entity_slug: str
    ) -> QuerySet:  # noqa: C901
        """
        Gets PhieuNhapChiPhiMuaHangModel instances within a date range.

        Parameters
        ----------
        start_date : str
            The start date (YYYY-MM-DD format).
        end_date : str
            The end date (YYYY-MM-DD format).
        entity_slug : str
            The entity slug.

        Returns
        -------
        QuerySet
            QuerySet of PhieuNhapChiPhiMuaHangModel instances.
        """
        return self.model_class.objects.filter(
            entity_model__slug=entity_slug,
            chung_tu_item__ngay_ct__range=[start_date, end_date],
        ).order_by('-created')

    def convert_chung_tu_uuids_to_model_instances(
        self, data: dict
    ) -> None:  # noqa: C901
        """
        Convert UUID strings to model instances for ChungTu fields specifically.

        Parameters
        ----------
        data: dict
            The data dictionary containing ChungTu fields to process.
        """
        import uuid

        from django_ledger.models import (  # noqa: F401,
            ChungTu,
            EntityUnitModel,
            QuyenChungTu,
        )

        # Handle ma_nk field (QuyenChungTu reference)
        if 'ma_nk' in data and (
            isinstance(data['ma_nk'], str) or isinstance(data['ma_nk'], uuid.UUID)
        ):
            try:
                data['ma_nk'] = QuyenChungTu.objects.get(uuid__exact=data['ma_nk'])
            except QuyenChungTu.DoesNotExist:
                pass

        # Handle chung_tu field (ChungTu reference)
        if 'chung_tu' in data and (
            isinstance(data['chung_tu'], str) or isinstance(data['chung_tu'], uuid.UUID)
        ):
            try:
                data['chung_tu'] = ChungTu.objects.get(uuid__exact=data['chung_tu'])
            except ChungTu.DoesNotExist:
                pass

        # Handle unit_id field (EntityUnitModel reference)
        if 'unit_id' in data and (
            isinstance(data['unit_id'], str) or isinstance(data['unit_id'], uuid.UUID)
        ):
            try:
                data['unit_id'] = EntityUnitModel.objects.get(
                    uuid__exact=data['unit_id']
                )
            except EntityUnitModel.DoesNotExist:
                pass
