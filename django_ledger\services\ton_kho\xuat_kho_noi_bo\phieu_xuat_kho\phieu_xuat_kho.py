"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) service implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import transaction  # noqa: F401
from django.db.models import QuerySet  # noqa: F401,

from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoModel,
)
from django_ledger.repositories.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoRepository,
)
from django_ledger.services.base import BaseService  # noqa: F401,
from django_ledger.services.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho.chi_tiet_phieu_xuat_kho import (  # noqa: F401,
    ChiTietPhieuXuatKhoService,
)


class PhieuXuatKhoService(BaseService):
    """
    Service class for PhieuXuatKhoModel.
    Handles business logic for the model.
    """

    def __init__(self):  # noqa: C901
        self.repository = PhieuXuatKhoRepository()
        self.chi_tiet_service = ChiTietPhieuXuatKhoService()
        super().__init__()



    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuXuatKhoModel]:  # noqa: C901
        """
        Retrieves a PhieuXuatKhoModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to retrieve.

        Returns
        -------
        Optional[PhieuXuatKhoModel]
            The PhieuXuatKhoModel with the given UUID, or None if not found.
        """
        return self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuXuatKhoModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuXuatKhoModel instances.
        """
        return self.repository.list(entity_slug=entity_slug, **kwargs)

    def _set_default_values(self, data: Dict[str, Any]) -> None:
        """
        Set default values for fields if not provided.

        Parameters
        ----------
        data : Dict[str, Any]
            The data dictionary to modify.
        """
        # Set default values if not provided
        if 'status' not in data:
            data['status'] = "draft"
        if 'ty_gia' not in data:
            data['ty_gia'] = 1.0
        if 'transfer_yn' not in data:
            data['transfer_yn'] = False
        if 't_so_luong' not in data:
            data['t_so_luong'] = 0
        if 't_tien_nt' not in data:
            data['t_tien_nt'] = 0
        if 't_tien' not in data:
            data['t_tien'] = 0

    @transaction.atomic
    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuXuatKhoModel:  # noqa: C901
        """
        Creates a new PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuXuatKhoModel.

        Returns
        -------
        PhieuXuatKhoModel
            The created PhieuXuatKhoModel instance.
        """
        # Set default values
        self._set_default_values(data)



        # Process related data
        chi_tiet = data.pop('chi_tiet', [])

        # Create the PhieuXuatKhoModel instance
        # ChungTu validation is handled by the ChungTuMixin in the repository
        instance = self.repository.create(entity_slug=entity_slug, data=data)

        # Process related data
        if chi_tiet:
            for chi_tiet_data in chi_tiet:
                self.chi_tiet_service.create(
                    phieu_xuat_kho_id=instance.uuid, data=chi_tiet_data
                )

        return instance

    @transaction.atomic
    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuXuatKhoModel]:  # noqa: C901
        """
        Updates an existing PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to update.
        data : Dict[str, Any]
            The data to update the PhieuXuatKhoModel with.

        Returns
        -------
        Optional[PhieuXuatKhoModel]
            The updated PhieuXuatKhoModel instance, or None if not found.
        """
        # Get current instance to check for so_ct changes
        current_instance = self.repository.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if not current_instance:
            return None



        # Process related data
        chi_tiet = data.pop('chi_tiet', [])

        # Update the PhieuXuatKhoModel instance
        # ChungTu validation is handled by the ChungTuMixin in the repository
        instance = self.repository.update(entity_slug=entity_slug, uuid=uuid, data=data)

        # Process related data
        if instance and chi_tiet:
            # First, delete existing chi_tiet
            existing_chi_tiet = self.chi_tiet_service.list_for_phieu_xuat_kho(
                phieu_xuat_kho_id=uuid
            )
            for chi_tiet_item in existing_chi_tiet:
                self.chi_tiet_service.delete(uuid=chi_tiet_item.uuid)
            # Then create new chi_tiet
            for chi_tiet_data in chi_tiet:
                self.chi_tiet_service.create(
                    phieu_xuat_kho_id=instance.uuid, data=chi_tiet_data
                )

        return instance

    @transaction.atomic
    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to delete.

        Returns
        -------
        bool
            True if the PhieuXuatKhoModel was deleted, False otherwise.
        """
        return self.repository.delete(entity_slug=entity_slug, uuid=uuid)

    def get_chi_tiet_data(
        self, phieu_xuat_kho_uuid: Optional[Union[str, UUID]] = None
    ) -> Dict[str, List]:  # noqa: C901
        """
        Get child data for serializer use.
        Instead of letting serializer query DB directly, service will fetch data and provide it to serializer.  # noqa: E501

        Args:
            phieu_xuat_kho_uuid: UUID of the parent object (if any)

        Returns:
            Dict containing child data
        """
        result = {}
        if phieu_xuat_kho_uuid:
            chi_tiet_list = self.chi_tiet_service.list_for_phieu_xuat_kho(
                phieu_xuat_kho_id=phieu_xuat_kho_uuid
            )
            result['chi_tiet'] = list(chi_tiet_list)
        return result
