"""
Simple test to debug the API issue
"""

from django.http import JsonResponse
from django.views import View
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django_ledger.services.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu.hoa_don_mua_dich_vu import HoaDonMuaDichVuService
from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu.hoa_don_mua_dich_vu import HoaDonMuaDichVuSerializer


@method_decorator(login_required, name='dispatch')
class SimpleTestView(View):
    def get(self, request, entity_slug):
        try:
            service = HoaDonMuaDichVuService()
            instances = service.list(entity_slug=entity_slug)
            
            # Test serializer without child_data
            serializer = HoaDonMuaDichVuSerializer(instances, many=True)
            data = serializer.data
            
            return JsonResponse({
                'success': True,
                'count': len(data),
                'data': data
            })
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)
