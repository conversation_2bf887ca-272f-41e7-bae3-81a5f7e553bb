"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Repository for PhieuThu model.
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import Q, QuerySet  # noqa: F401

from django_ledger.models import EntityModel, PhieuThuModel  # noqa: F401,
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuThuRepository(BaseRepository):
    """
    Repository class for handling PhieuThuModel database operations.
    Implements the Repository pattern for PhieuThuModel.
    """

    def __init__(self):  # noqa: C901
        """
        Initialize the repository with the PhieuThuModel.
        """
        super().__init__(model_class=PhieuThuModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuThuModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuThuModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model',
            'tk',
            'unit_id',
            'ma_nk',
            'so_ct',
            'ma_nt',
            'ma_tt',
            'ma_kh',
            'chung_tu_item',
        )

    def get_by_uuid(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> PhieuThuModel:  # noqa: C901
        """
        Gets a PhieuThuModel instance by UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuThuModel.

        Returns
        -------
        PhieuThuModel
            The PhieuThuModel instance.

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist.
        """
        return self.get_queryset().filter(entity_model__slug=entity_slug).get(uuid=uuid)

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuThuModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuThuModel instances.
        """
        qs = self.model_class.objects.filter(entity_model__slug=entity_slug)
        # Apply search if provided
        search_query = kwargs.get('search_query')
        if search_query:
            qs = qs.filter(
                Q(i_so_ct__icontains=search_query)
                | Q(dien_giai__icontains=search_query)
                | Q(ong_ba__icontains=search_query)
            )

        # Apply status filter if provided
        status = kwargs.get('status')
        if status:
            qs = qs.filter(status=status)
        # Apply date range filter if provided
        from_date = kwargs.get('from_date')
        to_date = kwargs.get('to_date')
        if from_date and to_date:
            qs = qs.filter(chung_tu_item__ngay_ct__range=[from_date, to_date])
        elif from_date:
            qs = qs.filter(chung_tu_item__ngay_ct__gte=from_date)
        elif to_date:
            qs = qs.filter(chung_tu_item__ngay_ct__lte=to_date)
        # Apply ordering
        return qs.order_by('-chung_tu_item__ngay_ct', '-created')

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuThuModel:  # noqa: C901
        """
        Creates a new PhieuThuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuThuModel.

        Returns
        -------
        PhieuThuModel
            The created PhieuThuModel instance.
        """
        entity_model = EntityModel.objects.get(slug=entity_slug)

        # Extract ChungTu fields from original data BEFORE UUID conversion
        chung_tu_fields = {}
        chung_tu_field_names = ['i_so_ct', 'ma_nk', 'so_ct', 'ngay_ct', 'ngay_lct']
        data_copy = data.copy()

        for field_name in chung_tu_field_names:
            if field_name in data_copy:
                chung_tu_fields[field_name] = data_copy.pop(field_name)

        # Convert UUID strings to model instances for remaining data
        processed_data = self.convert_uuids_to_model_instances(data_copy)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance
        instance.save()

        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> PhieuThuModel:  # noqa: C901
        """
        Updates an existing PhieuThuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuThuModel to update.
        data : Dict[str, Any]
            The data to update.

        Returns
        -------
        PhieuThuModel
            The updated PhieuThuModel instance.

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(entity_model__slug=entity_slug).get(
            uuid=uuid
        )

        # Convert UUID strings to model instances
        data_copy = self.convert_uuids_to_model_instances(data)
        # Update fields
        for key, value in data_copy.items():
            setattr(instance, key, value)

        instance.save()
        return instance

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuThuModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuThuModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.

        Raises
        ------
        PhieuThuModel.DoesNotExist
            If the instance does not exist.
        """
        instance = self.model_class.objects.filter(entity_model__slug=entity_slug).get(
            uuid=uuid
        )

        instance.delete()
        return True

    def convert_uuids_to_model_instances(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        CharField fields that should not be converted.
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'ma_ngv',
            'dia_chi',
            'ong_ba',
            'dien_giai',
            'status',
            'so_ct0',
            'so_ct_goc',
            'dien_giai_ct_goc',
            'id',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        return data_copy
