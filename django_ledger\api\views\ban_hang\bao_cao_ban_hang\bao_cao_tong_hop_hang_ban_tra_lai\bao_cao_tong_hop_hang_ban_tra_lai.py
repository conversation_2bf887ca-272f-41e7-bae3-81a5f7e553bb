"""
Sales Return Summary Report ViewSet.

This module provides the API endpoint for generating sales return summary reports
with proper validation, pagination, and error handling.
"""

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework import permissions
from django.core.exceptions import ValidationError
import logging

from django_ledger.api.views.common import ERPPagination
from django_ledger.api.serializers.ban_hang.bao_cao_ban_hang.bao_cao_tong_hop_hang_ban_tra_lai import (
    BaoCaoTongHopHangBanTraLaiRequestSerializer,
    BaoCaoTongHopHangBanTraLaiResponseSerializer,
)
from django_ledger.services.ban_hang.bao_cao_ban_hang.bao_cao_tong_hop_hang_ban_tra_lai import (
    BaoCaoTongHopHangBanTraLaiService
)

# Setup logging
logger = logging.getLogger(__name__)


class BaoCaoTongHopHangBanTraLaiViewSet(viewsets.ViewSet):
    """
    Sales Return Summary Report ViewSet.
    
    Provides single endpoint for getting sales return summary reports with filtering via POST body data.
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = None  # Will be initialized when needed

    def get_report(self, request, entity_slug):
        """
        Generate sales return summary report with filtering via POST body data.

        Parameters
        ----------
        request : Request
            The request object containing POST body data for filtering
        entity_slug : str
            The entity slug

        Returns
        -------
        Response
            The sales return summary report data with pagination
        """
        try:
            # Validate POST body data
            serializer = BaoCaoTongHopHangBanTraLaiRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {
                        "detail": "Invalid parameters",
                        "errors": serializer.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get validated data
            validated_data = serializer.validated_data
            
            # Initialize service if not already done
            if self.service is None:
                self.service = BaoCaoTongHopHangBanTraLaiService()
            
            # Generate report using service
            report_data = self.service.get_report(
                entity_slug=entity_slug, 
                filters=validated_data,
                user=request.user
            )

            # Handle empty data case
            if not report_data:
                return Response(
                    {"count": 0, "next": None, "previous": None, "results": []},
                    status=status.HTTP_200_OK,
                )

            # Apply pagination
            paginator = self.pagination_class()
            page = paginator.paginate_queryset(report_data, request)
            if page is not None:
                serializer = BaoCaoTongHopHangBanTraLaiResponseSerializer(
                    page,
                    many=True,
                    context={'mau_bc': validated_data.get('mau_bc', '10')}
                )
                return paginator.get_paginated_response(serializer.data)

            # If no pagination needed
            serializer = BaoCaoTongHopHangBanTraLaiResponseSerializer(
                report_data,
                many=True,
                context={'mau_bc': validated_data.get('mau_bc', '10')}
            )
            return Response(serializer.data, status=status.HTTP_200_OK)

        except ValidationError as e:
            logger.error(f"Validation error in sales return summary report: {str(e)}")
            return Response(
                {"detail": "Validation error", "errors": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except ValueError as e:
            logger.error(f"Value error in sales return summary report: {str(e)}")
            return Response(
                {"detail": "Invalid request", "errors": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(f"Unexpected error in sales return summary report: {str(e)}")
            return Response(
                {"detail": "Internal server error", "errors": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
