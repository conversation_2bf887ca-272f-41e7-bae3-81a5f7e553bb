"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThuePhieuNhapChiPhiMuaHangSerializer, which handles serialization  # noqa: E501
for the ThuePhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401
    ThuePhieuNhapChiPhiMuaHangModel,
)


class ThuePhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ThuePhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields following Django Ledger patterns
    ma_thue_data = serializers.SerializerMethodField()
    ma_kh_data = serializers.SerializerMethodField()
    tk_thue_no_data = serializers.SerializerMethodField()
    ma_mau_ct_data = serializers.SerializerMethodField()
    tk_thue_data = serializers.SerializerMethodField()
    ma_kh9_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_bp_data = serializers.SerializerMethodField()
    ma_vv_data = serializers.SerializerMethodField()
    ma_hd_data = serializers.SerializerMethodField()
    ma_dtt_data = serializers.SerializerMethodField()
    ma_ku_data = serializers.SerializerMethodField()
    ma_phi_data = serializers.SerializerMethodField()
    ma_sp_data = serializers.SerializerMethodField()
    ma_cp0_data = serializers.SerializerMethodField()

    class Meta:
        model = ThuePhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid',
            'phieu_nhap',
            'line',
            # Basic Information Fields (standardized names)
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            'ma_mau_bc',
            'ma_tc_thue',
            'ten_vt_thue',
            'dia_chi',
            'ma_so_thue',
            'ghi_chu',
            # Foreign Key Fields (standardized names)
            'ma_thue',
            'ma_thue_data',
            'ma_mau_ct',
            'ma_mau_ct_data',
            'ma_kh',
            'ma_kh_data',
            'tk_thue',
            'tk_thue_data',
            'ma_kh9',
            'ma_kh9_data',
            'ma_tt',
            'ma_tt_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_cp0',
            'ma_cp0_data',
            # Monetary Fields
            'tien_hang_tes',
            'tien_hang',
            'thue_tes',
            'thue',
            # Customer and Account information
            'ten_kh_thue',
            't_tien_nt',
            't_tien',
            'tk_thue_no',
            'tk_thue_no_data',
            'ten_tk_thue_no',
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def _validate_uuid_field(self, value, field_name):
        """
        Helper method to validate UUID fields consistently.
        """
        if value == "" or value is None:
            return None

        # If value is a model instance, extract its UUID
        if hasattr(value, 'uuid'):
            return str(value.uuid)

        # If value is already a string, clean and validate UUID format
        if isinstance(value, str):
            # Strip any whitespace or unwanted characters
            cleaned_value = value.strip()
            if not cleaned_value:
                return None
            try:
                from uuid import UUID
                UUID(cleaned_value)
                return cleaned_value
            except (ValueError, TypeError):
                raise serializers.ValidationError(f"'{cleaned_value}' is not a valid UUID for field {field_name}.")

        # For UUID objects, convert to string
        try:
            from uuid import UUID
            if isinstance(value, UUID):
                return str(value)
        except (ValueError, TypeError):
            pass

        # If we get here, the value is not a valid UUID format
        raise serializers.ValidationError(f"'{value}' is not a valid UUID for field {field_name}.")

    def validate_ma_thue(self, value):
        """
        Validate ma_thue field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_thue')

    def validate_ma_mau_ct(self, value):
        """
        Validate ma_mau_ct field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_mau_ct')

    def validate_ma_kh(self, value):
        """
        Validate ma_kh field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_kh')

    def validate_tk_thue(self, value):
        """
        Validate tk_thue field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'tk_thue')

    def validate_ma_kh9(self, value):
        """
        Validate ma_kh9 field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_kh9')

    def validate_ma_tt(self, value):
        """
        Validate ma_tt field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_tt')

    def validate_ma_bp(self, value):
        """
        Validate ma_bp field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_bp')

    def validate_ma_vv(self, value):
        """
        Validate ma_vv field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_vv')

    def validate_ma_hd(self, value):
        """
        Validate ma_hd field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_hd')

    def validate_ma_dtt(self, value):
        """
        Validate ma_dtt field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_dtt')

    def validate_ma_ku(self, value):
        """
        Validate ma_ku field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_ku')

    def validate_ma_phi(self, value):
        """
        Validate ma_phi field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_phi')

    def validate_ma_sp(self, value):
        """
        Validate ma_sp field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_sp')

    def validate_ma_cp0(self, value):
        """
        Validate ma_cp0 field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'ma_cp0')

    def validate_tk_thue_no(self, value):
        """
        Validate tk_thue_no field - convert empty string to None and validate UUID format.
        """
        return self._validate_uuid_field(value, 'tk_thue_no')

    def get_ma_thue_data(self, obj):  # noqa: C901
        """
        Returns the tax data for the ma_thue field.
        """
        if obj.ma_thue:
            return {
                'uuid': obj.ma_thue.uuid,
                'ma_thue': obj.ma_thue.ma_thue,
                'ten_thue': obj.ma_thue.ten_thue,
            }
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Returns the customer data for the ma_kh field.
        """
        if obj.ma_kh:
            return {
                'uuid': obj.ma_kh.uuid,
                'customer_code': obj.ma_kh.customer_code,
                'customer_name': obj.ma_kh.customer_name,
            }
        return None

    def get_tk_thue_no_data(self, obj):  # noqa: C901
        """
        Returns the account data for the tk_thue_no field.
        """
        if obj.tk_thue_no:
            return {
                'uuid': obj.tk_thue_no.uuid,
                'code': obj.tk_thue_no.code,
                'name': obj.tk_thue_no.name,
            }
        return None

    def get_ma_mau_ct_data(self, obj):  # noqa: C901
        """
        Returns the invoice template data for the ma_mau_ct field.
        """
        if obj.ma_mau_ct:
            return {
                'uuid': obj.ma_mau_ct.uuid,
                'ma_mau': getattr(obj.ma_mau_ct, 'ma_mau', None),
                'ten_mau': getattr(obj.ma_mau_ct, 'ten_mau', None),
            }
        return None

    def get_tk_thue_data(self, obj):  # noqa: C901
        """
        Returns the tax account data for the tk_thue field.
        """
        if obj.tk_thue:
            return {
                'uuid': obj.tk_thue.uuid,
                'code': obj.tk_thue.code,
                'name': obj.tk_thue.name,
            }
        return None

    def get_ma_kh9_data(self, obj):  # noqa: C901
        """
        Returns the tax office data for the ma_kh9 field.
        """
        if obj.ma_kh9:
            return {
                'uuid': obj.ma_kh9.uuid,
                'ma_cqt': getattr(obj.ma_kh9, 'ma_cqt', None),
                'ten_cqt': getattr(obj.ma_kh9, 'ten_cqt', None),
            }
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Returns the payment data for the ma_tt field.
        """
        if obj.ma_tt:
            return {
                'uuid': obj.ma_tt.uuid,
                'ma_tt': getattr(obj.ma_tt, 'ma_tt', None),
                'ten_tt': getattr(obj.ma_tt, 'ten_tt', None),
            }
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Returns the department data for the ma_bp field.
        """
        if obj.ma_bp:
            return {
                'uuid': obj.ma_bp.uuid,
                'ma_bp': obj.ma_bp.ma_bp,
                'ten_bp': obj.ma_bp.ten_bp,
            }
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Returns the case data for the ma_vv field.
        """
        if obj.ma_vv:
            return {
                'uuid': obj.ma_vv.uuid,
                'ma_vv': getattr(obj.ma_vv, 'ma_vv', None),
                'ten_vv': getattr(obj.ma_vv, 'ten_vv', None),
            }
        return None

    def get_ma_hd_data(self, obj):  # noqa: C901
        """
        Returns the contract data for the ma_hd field.
        """
        if obj.ma_hd:
            return {
                'uuid': obj.ma_hd.uuid,
                'ma_hd': getattr(obj.ma_hd, 'ma_hd', None),
                'ten_hd': getattr(obj.ma_hd, 'ten_hd', None),
            }
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Returns the payment term data for the ma_dtt field.
        """
        if obj.ma_dtt:
            return {
                'uuid': obj.ma_dtt.uuid,
                'ma_dtt': getattr(obj.ma_dtt, 'ma_dtt', None),
                'ten_dtt': getattr(obj.ma_dtt, 'ten_dtt', None),
            }
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Returns the loan data for the ma_ku field.
        """
        if obj.ma_ku:
            return {
                'uuid': obj.ma_ku.uuid,
                'ma_ku': getattr(obj.ma_ku, 'ma_ku', None),
                'ten_ku': getattr(obj.ma_ku, 'ten_ku', None),
            }
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Returns the fee data for the ma_phi field.
        """
        if obj.ma_phi:
            return {
                'uuid': obj.ma_phi.uuid,
                'ma_phi': getattr(obj.ma_phi, 'ma_phi', None),
                'ten_phi': getattr(obj.ma_phi, 'ten_phi', None),
            }
        return None

    def get_ma_sp_data(self, obj):  # noqa: C901
        """
        Returns the product data for the ma_sp field.
        """
        if obj.ma_sp:
            return {
                'uuid': obj.ma_sp.uuid,
                'ma_vt': getattr(obj.ma_sp, 'ma_vt', None),
                'ten_vt': getattr(obj.ma_sp, 'ten_vt', None),
            }
        return None

    def get_ma_cp0_data(self, obj):  # noqa: C901
        """
        Returns the invalid expense data for the ma_cp0 field.
        """
        if obj.ma_cp0:
            return {
                'uuid': obj.ma_cp0.uuid,
                'ma_cpkhl': getattr(obj.ma_cp0, 'ma_cpkhl', None),
                'ten_cpkhl': getattr(obj.ma_cp0, 'ten_cpkhl', None),
            }
        return None
