"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

PhieuXuatKho (Warehouse Export) repository implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    PhieuXuatKhoModel,
)
from django_ledger.repositories._utils.chung_tu_item_utils import (
    process_chung_tu_fields_extraction_and_conversion,
    update_instance_with_chung_tu_fields,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class PhieuXuatKhoRepository(BaseRepository):
    """
    Repository class for PhieuXuatKhoModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=PhieuXuatKhoModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for PhieuXuatKhoModel.

        Returns
        -------
        QuerySet
            The base queryset for PhieuXuatKhoModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model', 'unit_id', 'ma_nt', 'ma_kh',
            # ChungTu fields from ChungTuMixIn
            'chung_tu_item',
            'chung_tu_item__ma_nk',
            'chung_tu',
        ).prefetch_related(
            'chi_tiet_phieu_xuat_kho'
        )

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[PhieuXuatKhoModel]:  # noqa: C901
        """
        Retrieves a PhieuXuatKhoModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to retrieve.

        Returns
        -------
        Optional[PhieuXuatKhoModel]
            The PhieuXuatKhoModel with the given UUID, or None if not found.
        """
        try:
            return self.get_queryset().filter(entity_model__slug=entity_slug).get(
                uuid=uuid
            )
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists PhieuXuatKhoModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of PhieuXuatKhoModel instances.
        """
        return self.get_queryset().filter(entity_model__slug=entity_slug, **kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> PhieuXuatKhoModel:  # noqa: C901
        """
        Creates a new PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new PhieuXuatKhoModel.

        Returns
        -------
        PhieuXuatKhoModel
            The created PhieuXuatKhoModel instance.
        """
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)

        # Use utility function to extract and process ChungTu fields
        chung_tu_fields, remaining_data = process_chung_tu_fields_extraction_and_conversion(
            data
        )

        # Convert UUID strings to model instances for remaining data
        processed_data = self.convert_uuids_to_model_instances(remaining_data)

        # Create the instance without ChungTu fields first
        instance = self.model_class(entity_model=entity_model, **processed_data)

        # Set ChungTu fields using property setters
        for field_name, value in chung_tu_fields.items():
            setattr(instance, field_name, value)

        # Save the instance (triggers ChungTuMixIn logic)
        instance.save()
        return instance

    def update(
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[PhieuXuatKhoModel]:  # noqa: C901
        """
        Updates an existing PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to update.
        data : Dict[str, Any]
            The data to update the PhieuXuatKhoModel with.

        Returns
        -------
        Optional[PhieuXuatKhoModel]
            The updated PhieuXuatKhoModel instance, or None if not found.
        """
        # Get the instance
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Use utility function to handle ChungTu field updates
            return update_instance_with_chung_tu_fields(
                instance, data, self.convert_uuids_to_model_instances
            )
        return None

    def delete(self, entity_slug: str, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a PhieuXuatKhoModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the PhieuXuatKhoModel to delete.

        Returns
        -------
        bool
            True if the PhieuXuatKhoModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        PhieuXuatKho related fields.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'ma_gd',
            'ma_ngv',
            'dien_giai',
            'nguoi_tao',
            'xprogress',
            'xdatetime2',
            'xfile',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        # Handle specific foreign key conversions
        from django_ledger.models import (  # noqa: F401,
            CustomerModel,
            EntityUnitModel,
            NgoaiTeModel,
        )

        # Convert ma_kh UUID to CustomerModel instance
        if 'ma_kh' in data_copy and isinstance(data_copy['ma_kh'], str):
            try:
                data_copy['ma_kh'] = CustomerModel.objects.get(uuid=data_copy['ma_kh'])
            except CustomerModel.DoesNotExist:
                pass

        # Convert ma_nt UUID to NgoaiTeModel instance
        if 'ma_nt' in data_copy and isinstance(data_copy['ma_nt'], str):
            try:
                data_copy['ma_nt'] = NgoaiTeModel.objects.get(uuid=data_copy['ma_nt'])
            except NgoaiTeModel.DoesNotExist:
                pass

        # Convert unit_id UUID to EntityUnitModel instance
        if 'unit_id' in data_copy and isinstance(data_copy['unit_id'], str):
            try:
                data_copy['unit_id'] = EntityUnitModel.objects.get(uuid=data_copy['unit_id'])
            except EntityUnitModel.DoesNotExist:
                pass

        return data_copy


