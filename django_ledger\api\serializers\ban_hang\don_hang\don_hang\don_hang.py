"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for DonHang model.
"""

from rest_framework import serializers

from django_ledger.api.serializers._utils.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.api.serializers.customer import (  # noqa: F401,
    CustomerModelSerializer as CustomerSerializer,
)
from django_ledger.api.serializers.danh_muc import PhiSerializer  # noqa: F401,
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer as KheUocSerializer,
)
from django_ledger.api.serializers.danh_muc.ke_toan.ngoai_te import (  # noqa: F401,
    NgoaiTeSerializer,
)
from django_ledger.api.serializers.dia_chi import (  # noqa: F401,
    DiaChiSerializer,
)
from django_ledger.api.serializers.erp import (  # noqa: F401,
    PhuongThucThanhToanModelSerializer,
)
from django_ledger.api.serializers.han_thanh_toan import (  # noqa: F401,
    HanThanhToanModelSerializer as HanThanhToanSerializer,
)
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer as BoPhanSerializer,
)
from django_ledger.api.serializers.phuong_tien_giao_hang import (  # noqa: F401,
    PhuongTienGiaoHangModelSerializer,
)
from django_ledger.api.serializers.phuong_tien_van_chuyen import (  # noqa: F401,
    PhuongTienVanChuyenModelSerializer,
)
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer as DotThanhToanSerializer,
)
from django_ledger.api.serializers.unit import (  # noqa: F401,
    EntityUnitModelSerializer as EntityUnitSerializer,
)
from django_ledger.api.serializers.vu_viec import (  # noqa: F401,
    VuViecModelSerializer as VuViecSerializer,
)
from django_ledger.models import DonHangModel  # noqa: F401,


class DonHangSerializer(ChungTuSerializerMixin, serializers.ModelSerializer):
    """
    Serializer for DonHang model.
    Uses ChungTuSerializerMixin to automatically provide ChungTu fields.
    """

    # Related data fields (read-only)
    ma_nt_data = serializers.SerializerMethodField(read_only=True)
    ma_kh_data = serializers.SerializerMethodField(read_only=True)
    ma_bp_data = serializers.SerializerMethodField(read_only=True)
    ma_vv_data = serializers.SerializerMethodField(read_only=True)
    ma_dtt_data = serializers.SerializerMethodField(read_only=True)
    ma_ku_data = serializers.SerializerMethodField(read_only=True)
    ma_phi_data = serializers.SerializerMethodField(read_only=True)
    ma_tt_data = serializers.SerializerMethodField(read_only=True)
    unit_id_data = serializers.SerializerMethodField(read_only=True)
    # Missing _data fields for highlighted fields
    ma_dc_data = serializers.SerializerMethodField(read_only=True)
    ma_ptvc_data = serializers.SerializerMethodField(read_only=True)
    ma_pttt_data = serializers.SerializerMethodField(read_only=True)
    ma_ptgh_data = serializers.SerializerMethodField(read_only=True)

    # Child data
    chi_tiet_data = serializers.SerializerMethodField(read_only=True)
    chi_tiet = serializers.ListField(required=False, write_only=True)

    class Meta:
        model = DonHangModel
        fields = [
            'uuid',
            'entity_model',
            'i_so_ct',
            'ma_nk',
            'so_ct',
            'ngay_ct',
            'ngay_lct',
            'chung_tu',
            'ma_nt',
            'ma_nt_data',
            'ma_kh',
            'ma_kh_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_tt',
            'ma_tt_data',
            'unit_id',
            'unit_id_data',
            'ma_dc',
            'ma_dc_data',
            'ma_ptvc',
            'ma_ptvc_data',
            'ma_pttt',
            'ma_pttt_data',
            'ma_ptgh',
            'ma_ptgh_data',
            'ma_gd',
            'ma_ngv',
            'ma_nvbh',
            'ma_so_thue',
            'ong_ba',
            'dia_chi',
            'e_mail',
            'dien_giai',
            'ngay_hl',
            'so_ct2',
            'ty_gia',
            'status',
            'transfer_yn',
            'treo_dh',
            't_so_luong',
            't_tien_nt2',
            't_tien2',
            't_thue_nt',
            't_thue',
            't_ck_nt',
            't_ck',
            't_tt_nt',
            't_tt',
            'xfile',
            'chi_tiet_data',
            'chi_tiet',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_nt_data',
            'ma_kh_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_tt_data',
            'unit_id_data',
            'ma_dc_data',
            'ma_ptvc_data',
            'ma_pttt_data',
            'ma_ptgh_data',
            'chi_tiet_data',
            'created',
            'updated',
        ]

    def get_ma_nt_data(self, obj):  # noqa: C901
        """
        Get currency data.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None

    def get_ma_kh_data(self, obj):  # noqa: C901
        """
        Get customer data.
        """
        if obj.ma_kh:
            return CustomerSerializer(obj.ma_kh).data
        return None

    def get_ma_bp_data(self, obj):  # noqa: C901
        """
        Get department data.
        """
        if obj.ma_bp:
            return BoPhanSerializer(obj.ma_bp).data
        return None

    def get_ma_vv_data(self, obj):  # noqa: C901
        """
        Get case data.
        """
        if obj.ma_vv:
            return VuViecSerializer(obj.ma_vv).data
        return None

    def get_ma_dtt_data(self, obj):  # noqa: C901
        """
        Get payment term data.
        """
        if obj.ma_dtt:
            return DotThanhToanSerializer(obj.ma_dtt).data
        return None

    def get_ma_ku_data(self, obj):  # noqa: C901
        """
        Get area data.
        """
        if obj.ma_ku:
            return KheUocSerializer(obj.ma_ku).data
        return None

    def get_ma_phi_data(self, obj):  # noqa: C901
        """
        Get fee data.
        """
        if obj.ma_phi:
            return PhiSerializer(obj.ma_phi).data
        return None

    def get_ma_tt_data(self, obj):  # noqa: C901
        """
        Get payment due data.
        """
        if obj.ma_tt:
            return HanThanhToanSerializer(obj.ma_tt).data
        return None

    def get_unit_id_data(self, obj):  # noqa: C901
        """
        Get entity unit data.
        """
        if obj.unit_id:
            return EntityUnitSerializer(obj.unit_id).data
        return None

    def get_ma_dc_data(self, obj):  # noqa: C901
        """
        Get address data.
        """
        if obj.ma_dc:
            return DiaChiSerializer(obj.ma_dc).data
        return None

    def get_ma_ptvc_data(self, obj):  # noqa: C901
        """
        Get shipping method data.
        """
        if obj.ma_ptvc:
            return PhuongTienVanChuyenModelSerializer(obj.ma_ptvc).data
        return None

    def get_ma_pttt_data(self, obj):  # noqa: C901
        """
        Get payment method data.
        """
        if obj.ma_pttt:
            return PhuongThucThanhToanModelSerializer(obj.ma_pttt).data
        return None

    def get_ma_ptgh_data(self, obj):  # noqa: C901
        """
        Get delivery method data.
        """
        if obj.ma_ptgh:
            return PhuongTienGiaoHangModelSerializer(obj.ma_ptgh).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get chi tiet details data.
        """
        from django_ledger.api.serializers.ban_hang.don_hang.don_hang.chi_tiet_don_hang import (  # noqa: F401,
            ChiTietDonHangSerializer,
        )

        chi_tiet = obj.chi_tiet.all()
        return ChiTietDonHangSerializer(chi_tiet, many=True).data
