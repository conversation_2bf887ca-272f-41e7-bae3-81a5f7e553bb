"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ViewSet for Tong Hop Nhap Xuat Ton <PERSON> (Inventory Summary by Warehouse Report).

This ViewSet provides enterprise-grade API endpoints for inventory summary reporting
with comprehensive filtering, pagination, and error handling capabilities.
"""

from rest_framework import viewsets, permissions, status
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, extend_schema_view

from django_ledger.api.decorators import api_exception_handler
from django_ledger.api.serializers.ton_kho.tong_hop_nhap_xuat_ton.tong_hop_nhap_xuat_ton_theo_kho import (
    TongHopNhapXuatTonTheoKhoRequestSerializer,
    TongHopNhapXuatTonTheoKhoResponseSerializer,
)
from django_ledger.api.views.common import ERPPagination


@extend_schema_view(
    list=extend_schema(
        summary="Get Inventory Summary by Warehouse Report",
        description="Get comprehensive inventory summary report by warehouse with filtering and pagination via POST body data",
        parameters=[TongHopNhapXuatTonTheoKhoRequestSerializer],
        responses={200: TongHopNhapXuatTonTheoKhoResponseSerializer(many=True)},
    )
)
class TongHopNhapXuatTonTheoKhoViewSet(viewsets.ViewSet):
    """
    ViewSet for Inventory Summary by Warehouse Report (Tong Hop Nhap Xuat Ton Theo Kho).

    Provides single endpoint for getting comprehensive inventory summary reports
    with filtering via POST body data. This follows enterprise ERP patterns
    for complex reporting with extensive filter capabilities.

    Features:
    - Opening balance calculations
    - Period movement tracking
    - Closing balance calculations
    - Comprehensive filtering options
    - Performance-optimized queries
    - Proper error handling and validation
    """

    permission_classes = [permissions.IsAuthenticated]
    pagination_class = ERPPagination

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = None  # Will be initialized when needed

    @api_exception_handler
    def get_report(self, request, entity_slug):
        """
        Generate inventory summary by warehouse report with filtering via POST body data.

        This endpoint implements enterprise-grade inventory reporting logic
        with comprehensive filtering capabilities and proper balance calculations.

        Parameters
        ----------
        request : Request
            The request object containing POST body data for filtering
        entity_slug : str
            The entity slug

        Returns
        -------
        Response
            The inventory summary report data with pagination
        """
        # Validate POST body data
        serializer = TongHopNhapXuatTonTheoKhoRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {
                    "detail": "Invalid parameters",
                    "errors": serializer.errors,
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get validated data
        validated_data = serializer.validated_data

        # Initialize service if not already done
        if self.service is None:
            from django_ledger.services.ton_kho.tong_hop_nhap_xuat_ton.tong_hop_nhap_xuat_ton_theo_kho import (
                TongHopNhapXuatTonTheoKhoService,
            )
            self.service = TongHopNhapXuatTonTheoKhoService()

        # Generate report using service
        report_data = self.service.generate_report(
            entity_slug=entity_slug, filters=validated_data
        )

        # Apply pagination
        paginator = self.pagination_class()
        paginated_data = paginator.paginate_queryset(report_data, request)

        # Serialize the response
        response_serializer = TongHopNhapXuatTonTheoKhoResponseSerializer(
            paginated_data, many=True
        )

        # Return paginated response
        return paginator.get_paginated_response(response_serializer.data)
