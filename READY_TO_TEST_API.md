# 🚀 READY TO TEST - HoaDonMuaDichVu API

## 📋 Quick Setup
- **Server**: `python manage.py runserver 8003`
- **Base URL**: `http://127.0.0.1:8003`
- **Token**: `Token d37d77e4655f5aff352da29d8b1953338193d389`
- **Entity**: `tutimi-dnus2xnc`

## 🎯 Essential Test Commands

### 1. List All Invoices
```bash
curl -X GET \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389'
```

### 2. Create New Invoice (MAIN TEST)
```bash
curl -X POST \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389' \
  -H 'Content-Type: application/json' \
  -d '{
    "ma_gd": "GD001",
    "pc_tao_yn": "N",
    "ma_httt": "TM",
    "loai_ck": "PT",
    "ck_tl_nt": 0.0,
    "ma_kh": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ma_so_thue": "0123456789",
    "du_cn_thu": 0.0,
    "dia_chi": "123 Main Street",
    "ong_ba": "Nguyen Van A",
    "ma_nvmh": "37899ca8-dfa9-4d85-9317-19fce573a518",
    "e_mail": "<EMAIL>",
    "tk": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ma_tt": "9bee56e5-9a5b-4eb3-a099-05e95203c4aa",
    "dien_giai": "Hoa don mua dich vu test",
    "id": "ID001",
    "unit_id": "7043926df26f463299c344f2c13ac6e4",
    "i_so_ct": "001",
    "ma_nk": "fac3ae01-26a0-4b01-9020-979e903c6e7c",
    "so_ct": "HD001",
    "ngay_ct": "2025-01-02",
    "ngay_lct": "2025-01-02",
    "so_ct0": "HD001",
    "so_ct2": "HD001-2",
    "ngay_ct0": "2025-01-02",
    "ma_nt": "9c765001-8c06-4fdf-b4d9-685177aaca92",
    "ty_gia": 1.0,
    "status": "1",
    "transfer_yn": "N",
    "ma_ngv": "NGV001",
    "pc_ngay_ct": "2025-01-02",
    "pc_ma_ct": "PC001",
    "pc_ten_ct": "Phieu chi test",
    "pc_ma_nk": "PC",
    "pc_ten_nk": "Phieu chi",
    "pc_tknh": "1111",
    "pc_tk": "1112",
    "pc_ten_tk": "Tai khoan ngan hang",
    "pc_t_tt_nt": 1000000.0,
    "so_ct_tt": "TT001",
    "t_thue_nt": 100000.0,
    "t_thue": 100000.0,
    "t_tien_nt": 1000000.0,
    "t_tien": 1000000.0,
    "t_ck_nt_ex": 0.0,
    "t_ck_ex": 0.0,
    "t_ck_nt": 0.0,
    "t_ck": 0.0,
    "t_tt_nt": 1100000.0,
    "t_tt": 1100000.0,
    "chi_tiet": [
      {
        "line": 1,
        "ma_dv": "DV001",
        "x_new_item": "N",
        "tk_vt": "6411",
        "ten_tk": "Chi phi dich vu",
        "dien_giai": "Dich vu tu van",
        "ma_lts": "LTS001",
        "dvt": "Gio",
        "so_luong": 10.0,
        "gia_nt": 100000.0,
        "tien_nt": 1000000.0,
        "tien_tck_nt": 1000000.0,
        "tl_ck": 0.0,
        "ck_nt": 0.0,
        "gia": 100000.0,
        "tien": 1000000.0,
        "tien_tck": 1000000.0,
        "ck": 0.0,
        "ma_thue": "VAT10",
        "thue_suat": 10.0,
        "thue_nt": 100000.0,
        "thue": 100000.0,
        "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
        "ma_vv": "********-b75e-4ef4-bb09-6d480ff9df0c",
        "ma_hd": "13c29a44-7aa8-44e6-9ee8-4cedc7d72ef1",
        "ma_dtt": "DTT001",
        "ma_ku": "26b41e71-8c6f-4637-9ce7-80fbb448ec18",
        "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d",
        "ma_sp": "SP001",
        "ma_lsx": "LSX001",
        "ma_cp0": "f3ff213c-03de-47e2-b56b-9ac20bdc724e",
        "id_tb": "TB001",
        "line_tb": 1
      }
    ],
    "thue": [
      {
        "line": 1,
        "i_so_ct": "001",
        "line_ct": 1,
        "ma_ts": "TS001",
        "ma_lts": "LTS001",
        "ngay_mua": "2025-01-02",
        "ngay_kh0": "2025-01-02",
        "so_ky_kh": 60,
        "ngay_kh_kt": "2030-01-02",
        "nguyen_gia_nt": 1000000.0,
        "gt_da_kh_nt": 0.0,
        "gt_cl_nt": 1000000.0,
        "gt_kh_ky_nt": 16666.67,
        "nguyen_gia": 1000000.0,
        "gt_da_kh": 0.0,
        "gt_cl": 1000000.0,
        "gt_kh_ky": 16666.67,
        "so_hieu_ts": "TS001-SN",
        "tk_ts": "2111",
        "tk_kh": "2118",
        "tk_cp": "6427",
        "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
        "ma_vv": "********-b75e-4ef4-bb09-6d480ff9df0c",
        "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d"
      }
    ]
  }'
```

### 3. Get Specific Invoice (Replace {invoice_uuid} with UUID from step 2)
```bash
curl -X GET \
  'http://127.0.0.1:8003/api/v1/entities/tutimi-dnus2xnc/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{invoice_uuid}/' \
  -H 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389'
```

## 🔧 Database UUIDs Used
- **Entity**: `b0bb20d59da44fd8884992b5faa6a4cc`
- **Unit**: `7043926df26f463299c344f2c13ac6e4`
- **Vendor**: `9c765001-8c06-4fdf-b4d9-685177aaca92`
- **Staff**: `37899ca8-dfa9-4d85-9317-19fce573a518`
- **Payment Terms**: `9bee56e5-9a5b-4eb3-a099-05e95203c4aa`
- **Document Series**: `fac3ae01-26a0-4b01-9020-979e903c6e7c`
- **Department**: `a212aa44-946c-4412-8d0a-ced665e1c427`
- **Case**: `********-b75e-4ef4-bb09-6d480ff9df0c`
- **Contract**: `13c29a44-7aa8-44e6-9ee8-4cedc7d72ef1`
- **Area**: `26b41e71-8c6f-4637-9ce7-80fbb448ec18`
- **Fee**: `78d2a014-86bd-43ff-ad43-3ccdf91b470d`
- **Cost Center**: `f3ff213c-03de-47e2-b56b-9ac20bdc724e`

## ✅ Expected Results
- **Status 201**: Invoice created successfully
- **Automatic Accounting**: DICHVU + THUE journal entries created
- **Response**: Full invoice object with UUID

## 📁 Complete Documentation
- **Full API Guide**: `api_test_hoa_don_mua_dich_vu_updated.md`
- **Database UUIDs**: `extracted_uuids.json`

## 🎯 Test Sequence
1. Start server: `python manage.py runserver 8003`
2. Run List command to verify API is working
3. Run Create command and save the returned UUID
4. Run Get specific command with the UUID
5. Check database for accounting entries

**Ready to test! 🚀**
