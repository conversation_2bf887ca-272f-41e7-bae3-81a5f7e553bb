"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Tong Hop Nhap Xuat Ton <PERSON> (Inventory Summary by Warehouse Report) business logic.

SQL Logic Reference: See tong_hop_nhap_xuat_ton_theo_kho_query.sql for detailed SQL queries and business logic.
Field Mappings: See warning.md for any missing field mappings from db_schema.txt.
"""

from typing import Any, Dict, List
from decimal import Decimal

from django_ledger.services.base import BaseService


class TongHopNhapXuatTonTheoKhoService(BaseService):
    """
    Service class for handling Inventory Summary by Warehouse Report business logic.
    This service orchestrates calls to other services to generate comprehensive inventory reports.

    Based on 20 years of ERP experience, this implements enterprise-grade inventory reporting
    with proper opening balance, movement tracking, and closing balance calculations.
    """

    def __init__(self):
        """
        Initialize the service with required services.
        """
        super().__init__()
        # Initialize services lazily to avoid circular imports
        self._stock_transaction_service = None
        self._warehouse_stock_audit_service = None

    @property
    def stock_transaction_service(self):
        """Lazy initialization of stock_transaction_service."""
        if self._stock_transaction_service is None:
            from django_ledger.services.stock_transaction import StockTransactionService
            self._stock_transaction_service = StockTransactionService()
        return self._stock_transaction_service

    @property
    def warehouse_stock_audit_service(self):
        """Lazy initialization of warehouse_stock_audit_service."""
        if self._warehouse_stock_audit_service is None:
            from django_ledger.services.warehouse_stock_audit import WarehouseStockAuditService
            self._warehouse_stock_audit_service = WarehouseStockAuditService()
        return self._warehouse_stock_audit_service

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate inventory summary by warehouse report with the given filters.

        This is the main entry point for the report generation. It implements
        enterprise-grade inventory reporting logic with proper balance calculations.

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including date range, warehouse, material, etc.

        Returns
        -------
        List[Dict[str, Any]]
            Report data list with inventory summary by warehouse
        """
        try:
            # Extract date range
            start_date = filters.get('ngay_ct1')
            end_date = filters.get('ngay_ct2')

            if not start_date or not end_date:
                return []

            # Get aggregated stock movements for the period using StockTransactionService
            aggregated_movements = self.stock_transaction_service.get_aggregated_stock_movements(
                entity_slug, filters, start_date, end_date
            )

            # Calculate opening balances (before start_date) using WarehouseStockAuditService
            opening_balances = self.warehouse_stock_audit_service.get_opening_balances_with_filters(
                entity_slug, filters, start_date
            )

            # Process and combine data using database-aggregated results
            report_data = self._process_inventory_summary_optimized(
                aggregated_movements, opening_balances, filters
            )

            return report_data

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Error generating inventory summary report: {str(e)}", exc_info=True
            )
            # Return empty list instead of raising exception
            return []


    def _process_inventory_summary_optimized(
        self, aggregated_movements: List[Dict[str, Any]], opening_balances: Dict[str, Dict[str, Any]], filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Process aggregated stock movements and opening balances to create inventory summary report.

        This method uses pre-aggregated data from database-level operations for optimal
        performance with large datasets, avoiding Python iteration over QuerySets.

        Parameters
        ----------
        aggregated_movements : List[Dict[str, Any]]
            Pre-aggregated movements from StockTransactionService.get_aggregated_stock_movements()
        opening_balances : Dict[str, Dict[str, Any]]
            Opening balances from WarehouseStockAuditService.get_opening_balances_with_filters()

        Returns
        -------
        List[Dict[str, Any]]
            Processed inventory summary data
        """
        # If no movements, return empty result
        if not aggregated_movements:
            return []

        # Process aggregated results
        detail_records = []
        stt = 1  # Start from 1 for detail records

        # Initialize totals for summary
        total_ton_dau = Decimal('0')
        total_du_dau = Decimal('0')
        total_sl_nhap = Decimal('0')
        total_tien_nhap = Decimal('0')
        total_sl_xuat = Decimal('0')
        total_tien_xuat = Decimal('0')
        total_ton_cuoi = Decimal('0')
        total_du_cuoi = Decimal('0')

        for data in aggregated_movements:
            # Create warehouse-material key for opening balance lookup
            key = f"{data['ma_kho__ma_kho']}_{data['ma_vt__ma_vt']}"
            opening = opening_balances.get(key, {})

            # Get opening balances
            ton_dau = opening.get('ton_dau', Decimal('0'))
            du_dau = opening.get('du_dau', Decimal('0'))

            # Get aggregated movements (already calculated at database level)
            sl_nhap = data['sl_nhap_total']
            tien_nhap = data['tien_nhap_total']
            sl_xuat = data['sl_xuat_total']
            tien_xuat = data['tien_xuat_total']

            # Calculate closing balances
            ton_cuoi = ton_dau + sl_nhap - sl_xuat
            du_cuoi = du_dau + tien_nhap - tien_xuat

            # Accumulate totals for summary
            total_ton_dau += ton_dau
            total_du_dau += du_dau
            total_sl_nhap += sl_nhap
            total_tien_nhap += tien_nhap
            total_sl_xuat += sl_xuat
            total_tien_xuat += tien_xuat
            total_ton_cuoi += ton_cuoi
            total_du_cuoi += du_cuoi

            # Build detail record
            detail_records.append({
                'stt': stt,
                'ma_kho': data['ma_kho__ma_kho'],
                'ten_kho': data['ma_kho__ten_kho'],
                'ma_vt': data['ma_vt__ma_vt'],
                'ten_vt': data['ma_vt__ten_vt'],
                'dvt': data['ma_vt__dvt'] or '',
                'nhom': data['ma_vt__ma_lvt'] or '',
                'ton_dau': ton_dau,
                'du_dau': du_dau,
                'sl_nhap': sl_nhap,
                'tien_nhap': tien_nhap,
                'sl_xuat': sl_xuat,
                'tien_xuat': tien_xuat,
                'ton_cuoi': ton_cuoi,
                'du_cuoi': du_cuoi,
            })

            stt += 1

        # Apply vt_ton filtering if specified
        filtered_records = self._apply_vt_ton_filter(detail_records, filters.get('vt_ton'))

        # Apply group_by grouping if specified
        grouped_records = self._apply_group_by(filtered_records, filters.get('group_by'))

        # Recalculate totals based on final processed records
        final_totals = self._calculate_filtered_totals(grouped_records)

        # Create summary record with stt = 0 (always displayed with recalculated totals)
        summary_record = {
            'stt': 0,
            'ma_kho': '',
            'ten_kho': '',
            'ma_vt': '',
            'ten_vt': 'Tổng cộng',
            'dvt': '',
            'nhom': '',
            'ton_dau': final_totals['total_ton_dau'],
            'du_dau': final_totals['total_du_dau'],
            'sl_nhap': final_totals['total_sl_nhap'],
            'tien_nhap': final_totals['total_tien_nhap'],
            'sl_xuat': final_totals['total_sl_xuat'],
            'tien_xuat': final_totals['total_tien_xuat'],
            'ton_cuoi': final_totals['total_ton_cuoi'],
            'du_cuoi': final_totals['total_du_cuoi'],
        }

        # Combine summary + final processed records (summary first)
        result = [summary_record] + grouped_records

        return result

    def _apply_vt_ton_filter(self, records: List[Dict[str, Any]], vt_ton: int = None) -> List[Dict[str, Any]]:
        """
        Apply vt_ton filtering to records based on ton_cuoi values.

        Parameters
        ----------
        records : List[Dict[str, Any]]
            Detail records to filter
        vt_ton : int, optional
            Filter type: None/blank=all, 1=ton_cuoi!=0, 2=ton_cuoi=0

        Returns
        -------
        List[Dict[str, Any]]
            Filtered records
        """
        if vt_ton is None:
            # No filtering, return all records
            return records
        elif vt_ton == 1:
            # Filter records with ton_cuoi != 0
            return [record for record in records if record['ton_cuoi'] != Decimal('0')]
        elif vt_ton == 2:
            # Filter records with ton_cuoi = 0
            return [record for record in records if record['ton_cuoi'] == Decimal('0')]
        else:
            # Invalid vt_ton value, return all records
            return records

    def _calculate_filtered_totals(self, filtered_records: List[Dict[str, Any]]) -> Dict[str, Decimal]:
        """
        Calculate totals for filtered records.

        Parameters
        ----------
        filtered_records : List[Dict[str, Any]]
            Filtered detail records

        Returns
        -------
        Dict[str, Decimal]
            Calculated totals for summary row
        """
        totals = {
            'total_ton_dau': Decimal('0'),
            'total_du_dau': Decimal('0'),
            'total_sl_nhap': Decimal('0'),
            'total_tien_nhap': Decimal('0'),
            'total_sl_xuat': Decimal('0'),
            'total_tien_xuat': Decimal('0'),
            'total_ton_cuoi': Decimal('0'),
            'total_du_cuoi': Decimal('0'),
        }

        for record in filtered_records:
            totals['total_ton_dau'] += record['ton_dau']
            totals['total_du_dau'] += record['du_dau']
            totals['total_sl_nhap'] += record['sl_nhap']
            totals['total_tien_nhap'] += record['tien_nhap']
            totals['total_sl_xuat'] += record['sl_xuat']
            totals['total_tien_xuat'] += record['tien_xuat']
            totals['total_ton_cuoi'] += record['ton_cuoi']
            totals['total_du_cuoi'] += record['du_cuoi']

        return totals

    def _apply_group_by(self, records: List[Dict[str, Any]], group_by: int = None) -> List[Dict[str, Any]]:
        """
        Apply group_by grouping to records following so_tien_gui_ngan_hang pattern.

        Parameters
        ----------
        records : List[Dict[str, Any]]
            Records to group
        group_by : int, optional
            Grouping type: None/blank=none, 1=loai_vt, 2=nh_vt1, 3=nh_vt2, 4=nh_vt3, 5=tk_vt

        Returns
        -------
        List[Dict[str, Any]]
            Grouped records
        """
        if group_by is None or not records:
            # No grouping, return original records
            return records

        # Determine grouping field based on group_by value
        group_field_mapping = {
            1: 'ma_lvt',   # Group by material category (loại vật tư)
            2: 'nh_vt1',   # Group by material group 1
            3: 'nh_vt2',   # Group by material group 2
            4: 'nh_vt3',   # Group by material group 3
            5: 'tk_gv',    # Group by material account (tài khoản giá vốn)
        }

        if group_by not in group_field_mapping:
            # Invalid group_by value, return original records
            return records

        group_field = group_field_mapping[group_by]

        # Group records by the specified field (following so_tien_gui_ngan_hang pattern)
        from collections import defaultdict
        groups = defaultdict(list)

        for record in records:
            # Get group key from the record
            # Note: We need to get the actual field value from the aggregated data
            group_key = self._get_group_key_from_record(record, group_field)
            groups[group_key].append(record)

        # Create grouped records with sub-summaries and details
        grouped_records = []
        stt = 1

        # Sort groups for consistent ordering
        sorted_groups = sorted(groups.items(), key=lambda x: x[0] or '')

        for group_key, group_records in sorted_groups:
            if not group_records:
                continue

            # Create sub-summary record for this group
            # Aggregate numeric fields within the same group
            total_ton_dau = sum(record['ton_dau'] for record in group_records)
            total_du_dau = sum(record['du_dau'] for record in group_records)
            total_sl_nhap = sum(record['sl_nhap'] for record in group_records)
            total_tien_nhap = sum(record['tien_nhap'] for record in group_records)
            total_sl_xuat = sum(record['sl_xuat'] for record in group_records)
            total_tien_xuat = sum(record['tien_xuat'] for record in group_records)
            total_ton_cuoi = sum(record['ton_cuoi'] for record in group_records)
            total_du_cuoi = sum(record['du_cuoi'] for record in group_records)

            # Create sub-summary record
            sub_summary_record = {
                'stt': stt,
                'ma_kho': '',  # Clear warehouse code for sub-summary
                'ten_kho': '',  # Clear warehouse name for sub-summary
                'ma_vt': '',   # Clear material code for sub-summary
                'ten_vt': self._get_group_display_name(group_key, group_field),
                'dvt': '',     # Clear unit for sub-summary
                'nhom': group_key or '',  # Show group key in nhom field
                'ton_dau': total_ton_dau,
                'du_dau': total_du_dau,
                'sl_nhap': total_sl_nhap,
                'tien_nhap': total_tien_nhap,
                'sl_xuat': total_sl_xuat,
                'tien_xuat': total_tien_xuat,
                'ton_cuoi': total_ton_cuoi,
                'du_cuoi': total_du_cuoi,
            }

            grouped_records.append(sub_summary_record)
            stt += 1

            # Add detail records for this group
            # Sort detail records within group by name (ten_kho, ten_vt)
            sorted_detail_records = sorted(group_records, key=lambda x: (x['ten_kho'], x['ten_vt']))

            for detail_record in sorted_detail_records:
                detail_record_copy = detail_record.copy()
                detail_record_copy['stt'] = stt
                grouped_records.append(detail_record_copy)
                stt += 1

        return grouped_records

    def _get_group_key_from_record(self, record: Dict[str, Any], group_field: str) -> str:
        """
        Get group key from record based on group field.
        Uses the aggregated data fields from StockTransactionService.
        """
        # Map group fields to aggregated data field names
        field_mapping = {
            'ma_lvt': 'ma_vt__ma_lvt',
            'nh_vt1': 'ma_vt__nh_vt1',
            'nh_vt2': 'ma_vt__nh_vt2',
            'nh_vt3': 'ma_vt__nh_vt3',
            'tk_gv': 'ma_vt__tk_gv'
        }

        aggregated_field = field_mapping.get(group_field)
        if aggregated_field:
            return str(record.get(aggregated_field, '')) or 'Unknown'
        else:
            return 'Unknown'

    def _get_group_display_name(self, group_key: str, group_field: str) -> str:
        """
        Get display name for the group.
        """
        if not group_key or group_key == 'Unknown':
            return f"{group_field} không xác định"
        return group_key
