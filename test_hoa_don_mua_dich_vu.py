#!/usr/bin/env python3
"""
Test script for HoaDonMuaDichVu API
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://127.0.0.1:8003"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "tutimi"

# Create basic auth header
auth_string = f"{USERNAME}:{PASSWORD}"
auth_bytes = auth_string.encode('ascii')
auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

headers = {
    'Authorization': f'Basic {auth_b64}',
    'Content-Type': 'application/json'
}

def test_create_hoa_don():
    """Test creating a new hoa don mua dich vu"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    data = {
        "ma_gd": "GD001",
        "pc_tao_yn": "N",
        "ma_httt": "TM",
        "loai_ck": "PT",
        "ck_tl_nt": 0.0,
        "ma_so_thue": "0123456789",
        "du_cn_thu": 0.0,
        "dia_chi": "123 Main Street",
        "ong_ba": "Nguyen Van A",
        "e_mail": "<EMAIL>",
        "tk": "3314d5f7-4cb9-4253-8833-525dcd23801f",
        "dien_giai": "Hoa don mua dich vu test",
        "id": "ID001",
        "unit_id": "7043926df26f463299c344f2c13ac6e4",
        "ma_nk": "c1a10a51-9d62-4b6a-8c62-76432af3ca03",
        "so_ct": "MDV1.01.25.000016",
        "i_so_ct": 16,
        "ngay_ct": "2025-01-02",
        "ngay_lct": "2025-01-02",
        "so_ct0": "HD001",
        "so_ct2": "HD001-2",
        "ngay_ct0": "2025-01-02",
        "ty_gia": 1.0,
        "status": "1",
        "transfer_yn": "N",
        "ma_ngv": "NGV001",
        "pc_ngay_ct": "2025-01-02",
        "pc_ma_ct": "PC001",
        "pc_ten_ct": "Phieu chi test",
        "pc_ma_nk": "PC",
        "pc_ten_nk": "Phieu chi",
        "pc_tknh": "1111",
        "pc_tk": "1112",
        "pc_ten_tk": "Tai khoan ngan hang",
        "pc_t_tt_nt": 1000000.0,
        "so_ct_tt": "TT001",
        "t_thue_nt": 100000.0,
        "t_thue": 100000.0,
        "t_tien_nt": 1000000.0,
        "t_tien": 1000000.0,
        "t_ck_nt_ex": 0.0,
        "t_ck_ex": 0.0,
        "t_ck_nt": 0.0,
        "t_ck": 0.0,
        "t_tt_nt": 1100000.0,
        "t_tt": 1100000.0,
        "chi_tiet": [
            {
                "line": 1,
                "ma_dv": "DV001",
                "x_new_item": "N",
                "tk_vt": "6411",
                "ten_tk": "Chi phi dich vu",
                "dien_giai": "Dich vu tu van",
                "ma_lts": "LTS001",
                "dvt": "Gio",
                "so_luong": 10.0,
                "gia_nt": 100000.0,
                "tien_nt": 1000000.0,
                "tien_tck_nt": 1000000.0,
                "tl_ck": 0.0,
                "ck_nt": 0.0,
                "gia": 100000.0,
                "tien": 1000000.0,
                "tien_tck": 1000000.0,
                "ck": 0.0,
                "ma_thue": "VAT10",
                "thue_suat": 10.0,
                "thue_nt": 100000.0,
                "thue": 100000.0,
                "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
                "ma_vv": "89794195-b75e-4ef4-bb09-6d480ff9df0c",
                "ma_hd": "13c29a44-7aa8-44e6-9ee8-4cedc7d72ef1",
                "ma_dtt": "DTT001",
                "ma_ku": "26b41e71-8c6f-4637-9ce7-80fbb448ec18",
                "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d",
                "ma_sp": "SP001",
                "ma_lsx": "LSX001",
                "ma_cp0": "f3ff213c-03de-47e2-b56b-9ac20bdc724e",
                "id_tb": "TB001",
                "line_tb": 1
            }
        ],
        "thue": [
            {
                "line": 1,
                "i_so_ct": "001",
                "line_ct": 1,
                "ma_ts": "TS001",
                "ma_lts": "LTS001",
                "ngay_mua": "2025-01-02",
                "ngay_kh0": "2025-01-02",
                "so_ky_kh": 60,
                "ngay_kh_kt": "2030-01-02",
                "nguyen_gia_nt": 1000000.0,
                "gt_da_kh_nt": 0.0,
                "gt_cl_nt": 1000000.0,
                "gt_kh_ky_nt": 16666.67,
                "nguyen_gia": 1000000.0,
                "gt_da_kh": 0.0,
                "gt_cl": 1000000.0,
                "gt_kh_ky": 16666.67,
                "so_hieu_ts": "TS001-SN",
                "tk_ts": "2111",
                "tk_kh": "2118",
                "tk_cp": "6427",
                "ma_bp": "a212aa44-946c-4412-8d0a-ced665e1c427",
                "ma_vv": "89794195-b75e-4ef4-bb09-6d480ff9df0c",
                "ma_phi": "78d2a014-86bd-43ff-ad43-3ccdf91b470d"
            }
        ]
    }

    print("Testing CREATE HoaDonMuaDichVu...")
    print(f"URL: {url}")

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

        if response.status_code == 201:
            print("✅ CREATE test PASSED")
            return response.json()
        else:
            print("❌ CREATE test FAILED")
            return None

    except Exception as e:
        print(f"❌ CREATE test ERROR: {e}")
        return None

def test_list_hoa_don():
    """Test listing hoa don mua dich vu"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    print("\nTesting LIST HoaDonMuaDichVu...")
    print(f"URL: {url}")

    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"Count: {data.get('count', 0)}")
            print("✅ LIST test PASSED")
            return data
        else:
            print(f"Response: {response.text}")
            print("❌ LIST test FAILED")
            return None

    except Exception as e:
        print(f"❌ LIST test ERROR: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Starting HoaDonMuaDichVu API Tests...")

    # Test LIST first
    list_result = test_list_hoa_don()

    # Test CREATE
    create_result = test_create_hoa_don()

    print("\n📊 Test Summary:")
    print(f"LIST: {'✅ PASSED' if list_result is not None else '❌ FAILED'}")
    print(f"CREATE: {'✅ PASSED' if create_result is not None else '❌ FAILED'}")
