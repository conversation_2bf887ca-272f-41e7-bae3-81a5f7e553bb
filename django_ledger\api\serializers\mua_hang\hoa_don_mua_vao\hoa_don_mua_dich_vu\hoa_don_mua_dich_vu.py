"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don <PERSON>u (Purchase Service Invoice) Serializer.
"""

from rest_framework import serializers

from django_ledger.api.serializers._utils.chung_tu_fields.chung_tu_fields import ChungTuSerializerMixin
from django_ledger.api.serializers.entity import EntityModelSerializer
from django_ledger.api.serializers.vendor import VendorModelSerializer
from django_ledger.api.serializers.danh_muc.ke_toan.ngoai_te import NgoaiTeSerializer
from django_ledger.api.serializers.unit import EntityUnitModelSerializer
from django_ledger.api.serializers.accounts import AccountModelSerializer
from django_ledger.api.serializers.han_thanh_toan import HanThanhToanModelSerializer
from django_ledger.api.serializers.nhan_vien import NhanVienModelSerializer
from django_ledger.models.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu import HoaDonMuaDichVuModel


class HoaDonMuaDichVuSerializer(ChungTuSerializerMixin, serializers.ModelSerializer):
    """
    Serializer for HoaDonMuaDichVuModel with parent-child relationship.
    """
    entity_model = EntityModelSerializer(read_only=True)

    # Reference data fields for foreign keys
    ma_kh_data = serializers.SerializerMethodField()
    ma_nt_data = serializers.SerializerMethodField()
    unit_id_data = serializers.SerializerMethodField()
    tk_data = serializers.SerializerMethodField()
    ma_tt_data = serializers.SerializerMethodField()
    ma_nvmh_data = serializers.SerializerMethodField()

    # Child data fields
    chi_tiet_data = serializers.SerializerMethodField()
    thue_data = serializers.SerializerMethodField()

    class Meta:
        model = HoaDonMuaDichVuModel
        fields = [
            'uuid',
            'entity_model',
            'ma_gd',
            'pc_tao_yn',
            'ma_httt',
            'loai_ck',
            'ck_tl_nt',
            'ma_kh',
            'ma_kh_data',
            'ma_so_thue',
            'du_cn_thu',
            'dia_chi',
            'ong_ba',
            'ma_nvmh',
            'ma_nvmh_data',
            'e_mail',
            'tk',
            'tk_data',
            'ma_tt',
            'ma_tt_data',
            'dien_giai',
            'id',
            'unit_id',
            'unit_id_data',
            # ChungTu fields (i_so_ct, ma_nk, so_ct, ngay_ct, ngay_lct) are handled by ChungTuSerializerMixin
            # DO NOT include them here as they are properties/handled by mixin
            'xdatetime2',
            'so_ct0',
            'so_ct2',
            'ngay_ct0',
            'ma_nt',
            'ma_nt_data',
            'ty_gia',
            'status',
            'transfer_yn',
            'ma_ngv',
            'pc_ngay_ct',
            'pc_ma_ct',
            'pc_ten_ct',
            'pc_ma_nk',
            'pc_ten_nk',
            'pc_tknh',
            'pc_tk',
            'pc_ten_tk',
            'pc_t_tt_nt',
            'so_ct_tt',
            't_thue_nt',
            't_thue',
            't_tien_nt',
            't_tien',
            't_ck_nt_ex',
            't_ck_ex',
            't_ck_nt',
            't_ck',
            't_tt_nt',
            't_tt',
            'chi_tiet_data',
            'thue_data',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'entity_model',
            'ma_kh_data',
            'ma_nt_data',
            'unit_id_data',
            'tk_data',
            'ma_tt_data',
            'ma_nvmh_data',
            'chi_tiet_data',
            'thue_data',
            'created',
            'updated',
        ]
        extra_kwargs = {
            # Make optional fields not required in serializer
            'pc_tao_yn': {'required': False},
            'ma_httt': {'required': False},
            'loai_ck': {'required': False},
            'ck_tl_nt': {'required': False},
            'ma_so_thue': {'required': False},
            'du_cn_thu': {'required': False},
            'dia_chi': {'required': False},
            'ong_ba': {'required': False},
            'e_mail': {'required': False},
            'id': {'required': False},
            'so_ct0': {'required': False},
            'so_ct2': {'required': False},
            'ngay_ct0': {'required': False},
            'ma_ngv': {'required': False},
            'pc_ngay_ct': {'required': False},
            'pc_ma_ct': {'required': False},
            'pc_ten_ct': {'required': False},
            'pc_ma_nk': {'required': False},
            'pc_ten_nk': {'required': False},
            'pc_tknh': {'required': False},
            'pc_tk': {'required': False},
            'pc_ten_tk': {'required': False},
            'pc_t_tt_nt': {'required': False},
            'so_ct_tt': {'required': False},
            't_thue_nt': {'required': False},
            't_thue': {'required': False},
            't_tien_nt': {'required': False},
            't_tien': {'required': False},
            't_ck_nt_ex': {'required': False},
            't_ck_ex': {'required': False},
            't_ck_nt': {'required': False},
            't_ck': {'required': False},
            't_tt_nt': {'required': False},
            't_tt': {'required': False},
            'ty_gia': {'required': False},
            'status': {'required': False},
            'transfer_yn': {'required': False},
            'xdatetime2': {'required': False},
        }

    def get_ma_kh_data(self, obj):
        """
        Returns the vendor data for the ma_kh field.
        """
        if obj.ma_kh:
            return VendorModelSerializer(obj.ma_kh).data
        return None

    def get_ma_nt_data(self, obj):
        """
        Returns the currency data for the ma_nt field.
        """
        if obj.ma_nt:
            return NgoaiTeSerializer(obj.ma_nt).data
        return None



    def get_unit_id_data(self, obj):
        """
        Returns the organization unit data for the unit_id field.
        """
        if obj.unit_id:
            return EntityUnitModelSerializer(obj.unit_id).data
        return None

    def get_tk_data(self, obj):
        """
        Returns the account data for the tk field.
        """
        if obj.tk:
            return AccountModelSerializer(obj.tk).data
        return None

    def get_ma_tt_data(self, obj):
        """
        Returns the payment terms data for the ma_tt field.
        """
        if obj.ma_tt:
            return HanThanhToanModelSerializer(obj.ma_tt).data
        return None

    def get_ma_nvmh_data(self, obj):
        """
        Returns the sales staff data for the ma_nvmh field.
        """
        if obj.ma_nvmh:
            return NhanVienModelSerializer(obj.ma_nvmh).data
        return None

    def get_chi_tiet_data(self, obj):
        """
        Get chi tiet (details) data.
        """
        from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu.chi_tiet_hoa_don_mua_dich_vu import (
            ChiTietHoaDonMuaDichVuSerializer,
        )

        # Get chi_tiet (details) - using related_name="chi_tiet"
        chi_tiet = obj.chi_tiet.all()
        return ChiTietHoaDonMuaDichVuSerializer(chi_tiet, many=True).data

    def get_thue_data(self, obj):
        """
        Get thue (tax) data.
        """
        from django_ledger.api.serializers.mua_hang.hoa_don_mua_vao.hoa_don_mua_dich_vu.thue_hoa_don_mua_dich_vu import (
            ThueHoaDonMuaDichVuSerializer,
        )

        # Get thue (tax) - using related_name="thue"
        thue = obj.thue.all()
        return ThueHoaDonMuaDichVuSerializer(thue, many=True).data

    def to_internal_value(self, data):
        """
        Override to handle chi_tiet and thue fields in input data.
        """
        # Extract chi_tiet and thue from input data if present
        chi_tiet_input = data.pop('chi_tiet', None)
        thue_input = data.pop('thue', None)

        # Call parent to_internal_value
        validated_data = super().to_internal_value(data)

        # Add chi_tiet and thue back to validated_data if they were in input
        if chi_tiet_input is not None:
            validated_data['chi_tiet'] = chi_tiet_input
        if thue_input is not None:
            validated_data['thue'] = thue_input

        return validated_data
