"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Hoa Don <PERSON> (Purchase Service Invoice) Model.
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError

from django_ledger.models.mixins import CreateUpdateMixIn
from django_ledger.models._mixins.chung_tu_mixins import ChungTuMixIn


class HoaDonMuaDichVuModelQuerySet(models.QuerySet):
    """
    Custom QuerySet for HoaDonMuaDichVuModel.
    """

    def for_entity(self, entity_slug: str, user_model=None):
        """
        Filter queryset for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by
        user_model : UserModel, optional
            The user model for permission checking

        Returns
        -------
        QuerySet
            Filtered queryset
        """
        qs = self.filter(entity_model__slug__exact=entity_slug)
        if user_model:
            qs = qs.filter(entity_model__admin=user_model)
        return qs


class HoaDonMuaDichVuModelManager(models.Manager):
    """
    Custom Manager for HoaDonMuaDichVuModel.
    """

    def get_queryset(self):
        """
        Get the base queryset.

        Returns
        -------
        QuerySet
            The base queryset
        """
        return HoaDonMuaDichVuModelQuerySet(self.model, using=self._db)

    def for_entity(self, entity_slug: str, user_model=None):
        """
        Get queryset for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug to filter by
        user_model : UserModel, optional
            The user model for permission checking

        Returns
        -------
        QuerySet
            Filtered queryset
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug, user_model=user_model)


class HoaDonMuaDichVuModelAbstract(ChungTuMixIn, CreateUpdateMixIn):
    """
    Abstract model for Purchase Service Invoice (Hoa Don Mua Dich Vu).

    This model represents a purchase service invoice in the ERP system.
    It contains all the necessary fields for managing service purchase transactions.

    Attributes
    ----------
    uuid : UUIDField
        Primary key, unique identifier for the invoice
    entity_model : ForeignKey
        The entity that this invoice belongs to
    ma_gd : CharField
        Transaction code
    pc_tao_yn : CharField
        Payment voucher created flag
    ma_httt : CharField
        Payment method code
    loai_ck : CharField
        Discount type
    ck_tl_nt : FloatField
        Discount percentage in foreign currency
    ma_kh : ForeignKey
        Customer/Vendor reference
    ma_so_thue : CharField
        Tax identification number
    du_cn_thu : FloatField
        Remaining amount to collect
    dia_chi : CharField
        Address
    ong_ba : CharField
        Contact person name
    ma_nvmh : ForeignKey
        Sales staff reference
    e_mail : CharField
        Email address
    tk : ForeignKey
        Account reference
    ten_tk : CharField
        Account name
    ma_tt : ForeignKey
        Payment terms reference
    dien_giai : TextField
        Description
    id : CharField
        Internal ID
    unit_id : ForeignKey
        Organization unit reference
    i_so_ct : CharField
        Document sequence number
    ten_nk : CharField
        Document series name
    ma_nk : ForeignKey
        Document series reference
    so_ct : CharField
        Document number
    ngay_ct : DateField
        Document date
    ngay_lct : DateField
        Last update date
    xdatetime2 : DateTimeField
        Extended datetime field
    so_ct0 : CharField
        Original document number
    so_ct2 : CharField
        Secondary document number
    ngay_ct0 : DateField
        Original document date
    ma_nt : ForeignKey
        Currency reference
    ty_gia : FloatField
        Exchange rate
    status : CharField
        Status
    transfer_yn : CharField
        Transfer flag
    ma_ngv : CharField
        Shipper code
    pc_ngay_ct : DateField
        Payment voucher date
    pc_ma_ct : CharField
        Payment voucher code
    pc_ten_ct : CharField
        Payment voucher name
    pc_ma_nk : CharField
        Payment voucher series code
    pc_ten_nk : CharField
        Payment voucher series name
    pc_tknh : CharField
        Payment voucher bank account
    pc_tk : CharField
        Payment voucher account
    pc_ten_tk : CharField
        Payment voucher account name
    pc_t_tt_nt : FloatField
        Payment voucher total amount in foreign currency
    so_ct_tt : CharField
        Payment document number
    t_thue_nt : FloatField
        Total tax in foreign currency
    t_thue : FloatField
        Total tax
    t_tien_nt : FloatField
        Total amount in foreign currency
    t_tien : FloatField
        Total amount
    t_ck_nt_ex : FloatField
        Total discount excluding tax in foreign currency
    t_ck_ex : FloatField
        Total discount excluding tax
    t_ck_nt : FloatField
        Total discount in foreign currency
    t_ck : FloatField
        Total discount
    t_tt_nt : FloatField
        Total payment in foreign currency
    t_tt : FloatField
        Total payment
    """

    uuid = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Entity relationship
    entity_model = models.ForeignKey(
        "django_ledger.EntityModel",
        on_delete=models.CASCADE,
        verbose_name=_("Entity Model"),
        related_name="hoa_don_mua_dich_vu",
        help_text=_("The entity that this purchase service invoice belongs to")
    )

    # Basic invoice information
    ma_gd = models.CharField(
        max_length=50,
        verbose_name=_("Mã giao dịch"),
        help_text=_("Transaction code for the purchase service invoice")
    )
    pc_tao_yn = models.CharField(
        max_length=10,
        verbose_name=_("PC tạo Y/N"),
        help_text=_("Payment voucher created flag")
    )
    ma_httt = models.CharField(
        max_length=50,
        verbose_name=_("Mã HTTT"),
        help_text=_("Payment method code")
    )
    loai_ck = models.CharField(
        max_length=50,
        verbose_name=_("Loại chiết khấu"),
        help_text=_("Discount type")
    )
    ck_tl_nt = models.FloatField(
        verbose_name=_("CK tỷ lệ NT"),
        help_text=_("Discount percentage in foreign currency")
    )

    # Customer/Vendor information
    ma_kh = models.ForeignKey(
        "django_ledger.CustomerModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã khách hàng"),
        related_name="hoa_don_mua_dich_vu_kh",
        help_text=_("Customer/Vendor reference")
    )
    ma_so_thue = models.CharField(
        max_length=50,
        verbose_name=_("Mã số thuế"),
        help_text=_("Tax identification number")
    )
    du_cn_thu = models.FloatField(
        verbose_name=_("Dư cần thu"),
        help_text=_("Remaining amount to collect")
    )
    dia_chi = models.CharField(
        max_length=255,
        verbose_name=_("Địa chỉ"),
        help_text=_("Address")
    )
    ong_ba = models.CharField(
        max_length=100,
        verbose_name=_("Ông/Bà"),
        help_text=_("Contact person name")
    )

    # Sales staff information
    ma_nvmh = models.ForeignKey(
        "django_ledger.NhanVienModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã nhân viên MH"),
        related_name="hoa_don_mua_dich_vu_nvmh",
        help_text=_("Sales staff reference")
    )

    # Contact information
    e_mail = models.CharField(
        max_length=100,
        verbose_name=_("Email"),
        help_text=_("Email address")
    )

    # Account information
    tk = models.ForeignKey(
        "django_ledger.AccountModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Tài khoản"),
        related_name="hoa_don_mua_dich_vu_tk",
        help_text=_("Account reference")
    )

    # Payment terms
    ma_tt = models.ForeignKey(
        "django_ledger.HanThanhToanModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã thanh toán"),
        related_name="hoa_don_mua_dich_vu_tt",
        help_text=_("Payment terms reference")
    )

    # Description
    dien_giai = models.TextField(
        verbose_name=_("Diễn giải"),
        help_text=_("Description of the purchase service invoice")
    )

    # Internal tracking
    id = models.CharField(
        max_length=50,
        verbose_name=_("ID"),
        help_text=_("Internal ID")
    )

    # Organization unit
    unit_id = models.ForeignKey(
        "django_ledger.EntityUnitModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Đơn vị"),
        related_name="hoa_don_mua_dich_vu_unit",
        help_text=_("Organization unit reference")
    )

    # Document information (extended fields not in ChungTuMixIn)
    xdatetime2 = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_("Thời gian mở rộng"),
        help_text=_("Extended datetime field")
    )

    # Additional document numbers
    so_ct0 = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ gốc"),
        help_text=_("Original document number")
    )
    so_ct2 = models.CharField(
        max_length=50,
        verbose_name=_("Số chứng từ 2"),
        help_text=_("Secondary document number")
    )
    ngay_ct0 = models.DateField(
        verbose_name=_("Ngày chứng từ gốc"),
        help_text=_("Original document date")
    )

    # Currency information
    ma_nt = models.ForeignKey(
        "django_ledger.NgoaiTeModel",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Mã ngoại tệ"),
        related_name="hoa_don_mua_dich_vu_nt",
        help_text=_("Currency reference")
    )
    ty_gia = models.FloatField(
        verbose_name=_("Tỷ giá"),
        help_text=_("Exchange rate")
    )

    # Status and transfer
    status = models.CharField(
        max_length=50,
        verbose_name=_("Trạng thái"),
        help_text=_("Status")
    )
    transfer_yn = models.CharField(
        max_length=10,
        verbose_name=_("Chuyển Y/N"),
        help_text=_("Transfer flag")
    )
    ma_ngv = models.CharField(
        max_length=50,
        verbose_name=_("Mã người giao vận"),
        help_text=_("Shipper code")
    )

    # Payment voucher information
    pc_ngay_ct = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("PC ngày chứng từ"),
        help_text=_("Payment voucher date")
    )
    pc_ma_ct = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("PC mã chứng từ"),
        help_text=_("Payment voucher code")
    )
    pc_ten_ct = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("PC tên chứng từ"),
        help_text=_("Payment voucher name")
    )
    pc_ma_nk = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("PC mã nhóm chứng từ"),
        help_text=_("Payment voucher series code")
    )
    pc_ten_nk = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("PC tên nhóm chứng từ"),
        help_text=_("Payment voucher series name")
    )
    pc_tknh = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("PC tài khoản ngân hàng"),
        help_text=_("Payment voucher bank account")
    )
    pc_tk = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("PC tài khoản"),
        help_text=_("Payment voucher account")
    )
    pc_ten_tk = models.CharField(
        max_length=100,
        blank=True,
        verbose_name=_("PC tên tài khoản"),
        help_text=_("Payment voucher account name")
    )
    pc_t_tt_nt = models.FloatField(
        null=True,
        blank=True,
        verbose_name=_("PC tổng thanh toán NT"),
        help_text=_("Payment voucher total amount in foreign currency")
    )
    so_ct_tt = models.CharField(
        max_length=50,
        blank=True,
        verbose_name=_("Số chứng từ thanh toán"),
        help_text=_("Payment document number")
    )

    # Financial totals
    t_thue_nt = models.FloatField(
        verbose_name=_("Tổng thuế NT"),
        help_text=_("Total tax in foreign currency")
    )
    t_thue = models.FloatField(
        verbose_name=_("Tổng thuế"),
        help_text=_("Total tax")
    )
    t_tien_nt = models.FloatField(
        verbose_name=_("Tổng tiền NT"),
        help_text=_("Total amount in foreign currency")
    )
    t_tien = models.FloatField(
        verbose_name=_("Tổng tiền"),
        help_text=_("Total amount")
    )
    t_ck_nt_ex = models.FloatField(
        verbose_name=_("Tổng CK NT (không thuế)"),
        help_text=_("Total discount excluding tax in foreign currency")
    )
    t_ck_ex = models.FloatField(
        verbose_name=_("Tổng CK (không thuế)"),
        help_text=_("Total discount excluding tax")
    )
    t_ck_nt = models.FloatField(
        verbose_name=_("Tổng chiết khấu NT"),
        help_text=_("Total discount in foreign currency")
    )
    t_ck = models.FloatField(
        verbose_name=_("Tổng chiết khấu"),
        help_text=_("Total discount")
    )
    t_tt_nt = models.FloatField(
        verbose_name=_("Tổng thanh toán NT"),
        help_text=_("Total payment in foreign currency")
    )
    t_tt = models.FloatField(
        verbose_name=_("Tổng thanh toán"),
        help_text=_("Total payment")
    )

    # Ledger relationship for 1:1 mapping with service invoice
    ledger = models.OneToOneField(
        'django_ledger.LedgerModel',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        verbose_name=_("Sổ cái"),
        help_text=_("Sổ cái được tạo cho hóa đơn mua dịch vụ này"),
        related_name="hoa_don_mua_dich_vu",
    )

    objects = HoaDonMuaDichVuModelManager()

    class Meta:
        abstract = True
        verbose_name = _("Hóa đơn mua dịch vụ")
        verbose_name_plural = _("Hóa đơn mua dịch vụ")
        indexes = [
            models.Index(fields=["entity_model"]),
            models.Index(fields=["ma_kh"]),
            models.Index(fields=["status"]),
        ]

    def __str__(self):
        return f"{self.so_ct} - {self.dien_giai[:50]}"

    def clean(self):
        """
        Validate the model data.
        """
        super().clean()

        # Validate required fields
        if not self.ma_gd:
            raise ValidationError(_("Transaction code is required"))

        if not self.dien_giai:
            raise ValidationError(_("Description is required"))


class HoaDonMuaDichVuModel(HoaDonMuaDichVuModelAbstract):
    """
    Concrete implementation of HoaDonMuaDichVuModel.
    """

    class Meta(HoaDonMuaDichVuModelAbstract.Meta):
        abstract = False
        db_table = "hoa_don_mua_dich_vu"
