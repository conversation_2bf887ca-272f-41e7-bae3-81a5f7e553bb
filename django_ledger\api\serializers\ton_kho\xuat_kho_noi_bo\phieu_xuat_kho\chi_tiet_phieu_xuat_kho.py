"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatKho (Warehouse Export Detail) serializer implementation.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.account import (  # noqa: F401
    AccountModelSerializer,
)
from django_ledger.api.serializers.contract import (  # noqa: F401
    ContractModelSerializer,
)
from django_ledger.api.serializers.danh_muc import (  # noqa: F401,
    ChiPhiKhongHopLeSerializer,
    PhiSerializer,
)
from django_ledger.api.serializers.danh_muc.hop_dong_khe_uoc.khe_uoc.khe_uoc import (  # noqa: F401,
    KheUocModelSerializer,
)
from django_ledger.api.serializers.don_vi_tinh import (  # noqa: F401,
    DonViTinhSerializer,
)
from django_ledger.api.serializers.lo import (  # noqa: F401,
    LoModelSerializer,
)
from django_ledger.api.serializers.nhap_xuat import (  # noqa: F401,
    NhapXuatModelSerializer,
)
from django_ledger.api.serializers.organization import (  # noqa: F401,
    BoPhanModelSerializer,
)
from django_ledger.api.serializers.tien_do_thanh_toan import (  # noqa: F401,
    DotThanhToanModelSerializer,
)

# Import serializers for foreign key fields
from django_ledger.api.serializers.vat_tu import (  # noqa: F401,
    VatTuSerializer,
)
from django_ledger.api.serializers.vi_tri import (  # noqa: F401,
    ViTriModelSerializer,
)
from django_ledger.api.serializers.vu_viec import (  # noqa: F401,
    VuViecModelSerializer,
)
from django_ledger.api.serializers.warehouse import (  # noqa: F401,
    KhoHangModelSerializer,
)
from django_ledger.models.ton_kho.xuat_kho_noi_bo.phieu_xuat_kho import (  # noqa: F401,
    ChiTietPhieuXuatKhoModel,
)


class ChiTietPhieuXuatKhoModelSerializer(serializers.ModelSerializer):
    """
    Serializer for the ChiTietPhieuXuatKhoModel.

    This serializer handles the conversion between ChiTietPhieuXuatKhoModel instances and JSON representations,  # noqa: E501
    supporting both serialization (model to JSON) and deserialization (JSON to model).

    Key features:
    - Maintains original UUID references for foreign key fields
    - Adds additional fields with "_data" suffix that contain the complete nested object data  # noqa: E501
    - When deserializing, accepts UUIDs for reference fields
    """

    # Add nested serializers for foreign key fields
    phieu_xuat_kho_data = serializers.SerializerMethodField(read_only=True)
    ma_vt_data = VatTuSerializer(source='ma_vt', read_only=True)
    dvt_data = DonViTinhSerializer(source='dvt', read_only=True)
    ma_kho_data = KhoHangModelSerializer(source='ma_kho', read_only=True)
    ma_lo_data = LoModelSerializer(source='ma_lo', read_only=True)
    ma_vi_tri_data = ViTriModelSerializer(source='ma_vi_tri', read_only=True)
    tk_vt_data = AccountModelSerializer(source='tk_vt', read_only=True)
    ma_nx_data = NhapXuatModelSerializer(source='ma_nx', read_only=True)
    tk_du_data = AccountModelSerializer(source='tk_du', read_only=True)
    ma_bp_data = BoPhanModelSerializer(source='ma_bp', read_only=True)
    ma_vv_data = VuViecModelSerializer(source='ma_vv', read_only=True)
    ma_hd_data = ContractModelSerializer(source='ma_hd', read_only=True)
    ma_dtt_data = DotThanhToanModelSerializer(source='ma_dtt', read_only=True)
    ma_ku_data = KheUocModelSerializer(source='ma_ku', read_only=True)
    ma_phi_data = PhiSerializer(source='ma_phi', read_only=True)
    ma_sp_data = VatTuSerializer(source='ma_sp', read_only=True)
    ma_cp0_data = ChiPhiKhongHopLeSerializer(source='ma_cp0', read_only=True)

    class Meta:
        model = ChiTietPhieuXuatKhoModel
        fields = [
            'uuid',
            'phieu_xuat_kho',
            'phieu_xuat_kho_data',
            'line',
            'ma_vt',
            'ma_vt_data',
            'dvt',
            'dvt_data',
            'ten_dvt',
            'ma_kho',
            'ma_kho_data',
            'ten_kho',
            'ma_lo',
            'ma_lo_data',
            'ten_lo',
            'lo_yn',
            'ma_vi_tri',
            'ma_vi_tri_data',
            'ten_vi_tri',
            'vi_tri_yn',
            'he_so',
            'qc_yn',
            'so_luong',
            'px_dd',
            'gia_nt',
            'tien_nt',
            'gia',
            'tien',
            'tk_vt',
            'tk_vt_data',
            'ma_nx',
            'ma_nx_data',
            'tk_du',
            'tk_du_data',
            'ma_bp',
            'ma_bp_data',
            'ma_vv',
            'ma_vv_data',
            'ma_hd',
            'ma_hd_data',
            'ma_dtt',
            'ma_dtt_data',
            'ma_ku',
            'ma_ku_data',
            'ma_phi',
            'ma_phi_data',
            'ma_sp',
            'ma_sp_data',
            'ma_lsx',
            'ma_cp0',
            'ma_cp0_data',
            'sl_px',
            'id_px',
            'line_px',
            'id_yc',
            'line_yc',
            'id_nhap',
            'line_nhap',
            'created',
            'updated',
        ]
        read_only_fields = [
            'uuid',
            'phieu_xuat_kho_data',
            'ma_vt_data',
            'dvt_data',
            'ma_kho_data',
            'ma_lo_data',
            'ma_vi_tri_data',
            'tk_vt_data',
            'ma_nx_data',
            'tk_du_data',
            'ma_bp_data',
            'ma_vv_data',
            'ma_hd_data',
            'ma_dtt_data',
            'ma_ku_data',
            'ma_phi_data',
            'ma_sp_data',
            'ma_cp0_data',
            'created',
            'updated',
        ]

    def get_phieu_xuat_kho_data(self, obj):  # noqa: C901
        """
        Get basic data for the PhieuXuatKho.
        This is a method field that returns a simplified representation of the PhieuXuatKho.  # noqa: E501

        Parameters
        ----------
        obj : ChiTietPhieuXuatKhoModel
            The ChiTietPhieuXuatKhoModel instance

        Returns
        -------
        dict
            Dictionary with basic PhieuXuatKho data
        """
        if obj.phieu_xuat_kho:
            return {
                'uuid': str(obj.phieu_xuat_kho.uuid),
                'ma_gd': obj.phieu_xuat_kho.ma_gd,
                'dien_giai': obj.phieu_xuat_kho.dien_giai,
            }
        return None

    def to_internal_value(self, data):
        """
        Override to set default values for required fields that might be missing.
        """
        # Set default values for fields that are required in database but optional in API
        defaults = {
            'ten_dvt': '',
            'ten_kho': '',
            'ten_lo': '',
            'lo_yn': 0,
            'ten_vi_tri': '',
            'vi_tri_yn': 0,
            'he_so': 1,
            'qc_yn': 0,
            'px_dd': 0,
            'ma_lsx': '',
            'sl_px': 0,
            'id_px': 0,
            'line_px': 0,
            'id_yc': 0,
            'line_yc': 0,
            'id_nhap': 0,
            'line_nhap': 0,
        }

        # Apply defaults for missing fields
        for field, default_value in defaults.items():
            if field not in data:
                data[field] = default_value

        return super().to_internal_value(data)
