"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ChiPhiPhieuNhapChiPhiMuaHangSerializer, which handles serialization  # noqa: E501
for the ChiPhiPhieuNhapChiPhiMuaHangModel.
"""

from rest_framework import serializers  # noqa: F401

from django_ledger.models.mua_hang.hoa_don_mua_vao.phieu_nhap_chi_phi_mua_hang import (  # noqa: F401
    ChiPhiPhieuNhapChiPhiMuaHangModel,
)


class ChiPhiPhieuNhapChiPhiMuaHangSerializer(serializers.ModelSerializer):
    """
    Serializer for ChiPhiPhieuNhapChiPhiMuaHangModel.
    """

    # Reference data fields
    ma_cp_data = serializers.SerializerMethodField()

    class Meta:
        model = ChiPhiPhieuNhapChiPhiMuaHangModel
        fields = [
            'uuid',
            'phieu_nhap',
            'line',
            'ma_cp',
            'tien_cp_nt',
            'tien_cp',
            'ma_cp_data',
        ]
        read_only_fields = ['uuid', 'phieu_nhap']

    def validate_ma_cp(self, value):
        """
        Validate ma_cp field - convert empty string to None and validate UUID format.
        """
        if value == "" or value is None:
            return None

        # If value is a model instance (ChiPhiModel), extract its UUID
        if hasattr(value, 'uuid'):
            return str(value.uuid)

        # If value is already a string, validate UUID format
        if isinstance(value, str):
            try:
                from uuid import UUID
                UUID(value)
                return value
            except (ValueError, TypeError):
                raise serializers.ValidationError(f"'{value}' is not a valid UUID.")

        # For UUID objects, convert to string
        try:
            from uuid import UUID
            if isinstance(value, UUID):
                return str(value)
        except (ValueError, TypeError):
            pass

        # If we get here, the value is not a valid UUID format
        raise serializers.ValidationError(f"'{value}' is not a valid UUID.")

    def get_ma_cp_data(self, obj):  # noqa: C901
        """
        Returns the expense data for the ma_cp field.
        """
        if obj.ma_cp:
            return {
                'uuid': obj.ma_cp.uuid,
                'ma_cp': obj.ma_cp.ma_cp,
                'ten_cp': obj.ma_cp.ten_cp,
                'loai_pb': obj.ma_cp.loai_pb,
            }
        return None
