"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

This module implements the ThuePhieuNhapChiPhiMuaHangModel, which represents the taxes
associated with a purchase expense receipt.
"""

from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class ThuePhieuNhapChiPhiMuaHangModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the ThuePhieuNhapChiPhiMuaHangModel.
    """

    def for_phieu_nhap(self, phieu_nhap_id):  # noqa: C901
        """
        Returns taxes for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id: UUID
            The PhieuNhapChiPhiMuaHangModel UUID used for filtering the QuerySet.

        Returns
        -------
        ThuePhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of ThuePhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.filter(phieu_nhap_id=phieu_nhap_id)


class ThuePhieuNhapChiPhiMuaHangModelManager(Manager):
    """
    A custom defined ThuePhieuNhapChiPhiMuaHangModel Manager that will act as an interface to handle the  # noqa: E501
    ThuePhieuNhapChiPhiMuaHangModel database queries.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom ThuePhieuNhapChiPhiMuaHangModelQueryset.
        """
        return ThuePhieuNhapChiPhiMuaHangModelQueryset(
            self.model, using=self._db
        )

    def for_phieu_nhap(self, phieu_nhap_id):  # noqa: C901
        """
        Returns taxes for a specific purchase expense receipt.

        Parameters
        ----------
        phieu_nhap_id: UUID
            The PhieuNhapChiPhiMuaHangModel UUID used for filtering the QuerySet.

        Returns
        -------
        ThuePhieuNhapChiPhiMuaHangModelQueryset
            A QuerySet of ThuePhieuNhapChiPhiMuaHangModel with applied filters.
        """
        return self.get_queryset().for_phieu_nhap(phieu_nhap_id=phieu_nhap_id)


class ThuePhieuNhapChiPhiMuaHangModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the ThuePhieuNhapChiPhiMuaHangModel database will inherit from.  # noqa: E501
    The ThuePhieuNhapChiPhiMuaHangModel inherits functionality from the following MixIns:  # noqa: E501

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    ----------
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid4().  # noqa: E501
    phieu_nhap : ForeignKey
        Reference to the purchase expense receipt.
    line : IntegerField
        Line number.
    so_ct0 : CharField
        Original document number.
    so_ct2 : CharField
        Secondary document number.
    ngay_ct0 : DateTimeField
        Original document date.
    ma_mau_bc : CharField
        Report template.
    ma_tc_thue : CharField
        Nature code.
    ten_vt_thue : CharField
        Goods/services name.
    dia_chi : CharField
        Address.
    ma_so_thue : CharField
        Tax code.
    ghi_chu : CharField
        Notes.
    ma_thue : ForeignKey
        Tax code (reference to TaxModel).
    ma_mau_ct : ForeignKey
        Invoice template (reference to MauSoHdModel).
    ma_kh : ForeignKey
        Customer code (reference to CustomerModel).
    ten_kh_thue : CharField
        Tax customer name.
    tk_thue : ForeignKey
        Tax account (reference to AccountModel).
    ma_kh9 : ForeignKey
        Tax department (reference to CustomerModel).
    ma_tt : ForeignKey
        Payment code (reference to HanThanhToanModel).
    bo_phan : ForeignKey
        Department (reference to BoPhanModel).
    ma_vv : ForeignKey
        Case/matter (reference to VuViecModel).
    ma_hd : ForeignKey
        Contract (reference to ContractModel).
    dot_thanh_toan : ForeignKey
        Payment batch (reference to DotThanhToanModel).
    ma_ku : ForeignKey
        Agreement (reference to KheUocModel).
    phi : ForeignKey
        Fee (reference to PhiModel).
    ma_sp : ForeignKey
        Product (reference to VatTuModel).
    ma_cp0 : ForeignKey
        Invalid expenses (reference to ChiPhiKhongHopLeModel).
    tien_hang_tes : DecimalField
        Goods amount TES.
    tien_hang : DecimalField
        Goods amount.
    thue_tes : DecimalField
        Tax TES.
    thue : DecimalField
        Tax amount.
    so_ct0 : CharField
        Original document number.
    so_ct2 : CharField
        Secondary document number.
    ngay_ct0 : DateTimeField
        Original document date.
    ma_thue : ForeignKey
        Tax code.
    ma_kh : ForeignKey
        Customer code.
    ten_kh_thue : CharField
        Tax customer name.
    t_tien_nt : DecimalField
        Total amount in foreign currency.
    t_tien : DecimalField
        Total amount.
    tk_thue_no : ForeignKey
        Tax debit account.
    ten_tk_thue_no : CharField
        Tax debit account name.
    """

    uuid = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    phieu_nhap = models.ForeignKey(
        'django_ledger.PhieuNhapChiPhiMuaHangModel',
        on_delete=models.CASCADE,
        related_name='thue_phieu_nhaps',
        verbose_name=_('Phiếu nhập chi phí mua hàng'),
    )

    # Line information
    line = models.IntegerField(verbose_name=_("Số thứ tự"))

    # Basic Information Fields (standardized names)
    ma_mau_bc = models.CharField(
        max_length=50, verbose_name=_('Mẫu báo cáo'), null=True, blank=True
    )
    ma_tc_thue = models.CharField(
        max_length=50, verbose_name=_('Mã tính chất'), null=True, blank=True
    )
    ten_vt_thue = models.CharField(
        max_length=255, verbose_name=_('Tên hàng hóa dịch vụ'), null=True, blank=True
    )
    dia_chi = models.CharField(
        max_length=255, verbose_name=_('Địa chỉ'), null=True, blank=True
    )
    ma_so_thue = models.CharField(
        max_length=50, verbose_name=_('Mã số thuế'), null=True, blank=True
    )
    ghi_chu = models.CharField(
        max_length=255, verbose_name=_('Ghi chú'), null=True, blank=True
    )

    # Foreign Key Relationships (standardized names)
    ma_mau_ct = models.ForeignKey(
        'django_ledger.MauSoHDModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mẫu số hóa đơn'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    tk_thue = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản thuế'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang_tk_thue',
        null=True,
        blank=True,
    )
    ma_kh9 = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Cục thuế'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang_cuc_thue',
        null=True,
        blank=True,
    )
    ma_tt = models.ForeignKey(
        'django_ledger.HanThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thanh toán'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_bp = models.ForeignKey(
        'django_ledger.BoPhanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Bộ phận'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_vv = models.ForeignKey(
        'django_ledger.VuViecModel',
        on_delete=models.CASCADE,
        verbose_name=_('Vụ việc'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_hd = models.ForeignKey(
        'django_ledger.ContractModel',
        on_delete=models.CASCADE,
        verbose_name=_('Hợp đồng'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_dtt = models.ForeignKey(
        'django_ledger.DotThanhToanModel',
        on_delete=models.CASCADE,
        verbose_name=_('Đợt thanh toán'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_ku = models.ForeignKey(
        'django_ledger.KheUocModel',
        on_delete=models.CASCADE,
        verbose_name=_('Khế ước'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_phi = models.ForeignKey(
        'django_ledger.PhiModel',
        on_delete=models.CASCADE,
        verbose_name=_('Phí'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_sp = models.ForeignKey(
        'django_ledger.VatTuModel',
        on_delete=models.CASCADE,
        verbose_name=_('Sản phẩm'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )
    ma_cp0 = models.ForeignKey(
        'django_ledger.ChiPhiKhongHopLeModel',
        on_delete=models.CASCADE,
        verbose_name=_('Chi phí không hợp lệ'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
        blank=True,
    )

    # Monetary Fields
    tien_hang_tes = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tiền hàng TES'),
        null=True, blank=True, default=0
    )
    tien_hang = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tiền hàng'),
        null=True, blank=True, default=0
    )
    thue_tes = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Thuế TES'),
        null=True, blank=True, default=0
    )
    thue = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Thuế'),
        null=True, blank=True, default=0
    )

    # Document information (existing fields)
    so_ct0 = models.CharField(max_length=50, verbose_name=_('Số chứng từ 0'))
    so_ct2 = models.CharField(max_length=50, verbose_name=_('Số chứng từ 2'))
    ngay_ct0 = models.DateTimeField(verbose_name=_('Ngày chứng từ 0'))
    # Tax information (existing field)
    ma_thue = models.ForeignKey(
        'django_ledger.TaxModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã thuế'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang_ma_thue',
        null=True,
    )

    # Customer information
    ma_kh = models.ForeignKey(
        'django_ledger.CustomerModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã khách hàng'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang',
        null=True,
    )
    ten_kh_thue = models.CharField(
        max_length=255, verbose_name=_('Tên khách hàng thuế')
    )

    # Amount information
    t_tien_nt = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        verbose_name=_('Tổng tiền ngoại tệ'),
    )
    t_tien = models.DecimalField(
        max_digits=18, decimal_places=2, verbose_name=_('Tổng tiền')
    )

    # Account information
    tk_thue_no = models.ForeignKey(
        'django_ledger.AccountModel',
        on_delete=models.CASCADE,
        verbose_name=_('Tài khoản thuế nợ'),
        related_name='thue_phieu_nhap_chi_phi_mua_hang_tk_thue_no',
        null=True,
    )
    ten_tk_thue_no = models.CharField(
        max_length=255, verbose_name=_('Tên tài khoản thuế nợ')
    )

    objects = ThuePhieuNhapChiPhiMuaHangModelManager()

    class Meta:
        abstract = True
        verbose_name = _('Thuế phiếu nhập chi phí mua hàng')
        verbose_name_plural = _('Thuế phiếu nhập chi phí mua hàng')
        indexes = [
            models.Index(fields=['phieu_nhap']),
            models.Index(fields=['ma_thue']),
            models.Index(fields=['ma_kh']),
            models.Index(fields=['tk_thue_no']),
            models.Index(fields=['ma_mau_ct']),
            models.Index(fields=['tk_thue']),
            models.Index(fields=['ma_kh9']),
            models.Index(fields=['ma_tt']),
            models.Index(fields=['ma_bp']),
            models.Index(fields=['ma_vv']),
            models.Index(fields=['ma_hd']),
            models.Index(fields=['ma_dtt']),
            models.Index(fields=['ma_ku']),
            models.Index(fields=['ma_phi']),
            models.Index(fields=['ma_sp']),
            models.Index(fields=['ma_cp0']),
            
        ]
        ordering = ['line']

    def __str__(self):  # noqa: C901
        return f'{self.phieu_nhap} - {self.line}: {self.so_ct0 or self.ma_thue}'


class ThuePhieuNhapChiPhiMuaHangModel(ThuePhieuNhapChiPhiMuaHangModelAbstract):
    """
    Base ThuePhieuNhapChiPhiMuaHangModel Implementation
    """

    class Meta(ThuePhieuNhapChiPhiMuaHangModelAbstract.Meta):
        abstract = False
        db_table = 'thue_phieu_nhap_chi_phi_mua_hang'
