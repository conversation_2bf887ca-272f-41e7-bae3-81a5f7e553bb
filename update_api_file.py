#!/usr/bin/env python3
"""
Script to update API test file with correct URLs and headers
"""

def update_api_file():
    """Update the API test file with correct information."""
    
    # Read the current file
    with open('api_test_hoa_don_mua_dich_vu.md', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace all occurrences
    replacements = [
        ('http://localhost:8003', 'http://127.0.0.1:8003'),
        ('Authorization: Bearer YOUR_TOKEN_HERE', 'Authorization: Token d37d77e4655f5aff352da29d8b1953338193d389'),
        ('{entity_slug}', 'tutimi-dnus2xnc'),
        ('vendor-uuid-here', '9c765001-8c06-4fdf-b4d9-685177aaca92'),
        ('staff-uuid-here', '37899ca8-dfa9-4d85-9317-19fce573a518'),
        ('account-uuid-here', '9c765001-8c06-4fdf-b4d9-685177aaca92'),
        ('payment-terms-uuid-here', '9bee56e5-9a5b-4eb3-a099-05e95203c4aa'),
        ('unit-uuid-here', '7043926df26f463299c344f2c13ac6e4'),
        ('document-series-uuid-here', 'fac3ae01-26a0-4b01-9020-979e903c6e7c'),
        ('currency-uuid-here', '9c765001-8c06-4fdf-b4d9-685177aaca92'),
        ('department-uuid-here', 'a212aa44-946c-4412-8d0a-ced665e1c427'),
        ('case-uuid-here', '********-b75e-4ef4-bb09-6d480ff9df0c'),
        ('contract-uuid-here', '13c29a44-7aa8-44e6-9ee8-4cedc7d72ef1'),
        ('area-uuid-here', '26b41e71-8c6f-4637-9ce7-80fbb448ec18'),
        ('fee-uuid-here', '78d2a014-86bd-43ff-ad43-3ccdf91b470d'),
        ('cost-center-uuid-here', 'f3ff213c-03de-47e2-b56b-9ac20bdc724e'),
        ('{vendor_uuid}', '9c765001-8c06-4fdf-b4d9-685177aaca92')
    ]
    
    # Apply all replacements
    for old, new in replacements:
        content = content.replace(old, new)
    
    # Write back to file
    with open('api_test_hoa_don_mua_dich_vu_updated.md', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Updated API file saved as 'api_test_hoa_don_mua_dich_vu_updated.md'")
    print("📋 Applied replacements:")
    for old, new in replacements:
        print(f"   • {old} → {new}")

if __name__ == "__main__":
    update_api_file()
