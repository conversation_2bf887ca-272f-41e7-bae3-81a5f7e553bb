# Generated by Django 4.2.10 on 2025-07-01 09:04

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('django_ledger', '0125_alter_phieunhapchiphimuahangmodel_ma_tt_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='thue_suat',
        ),
        migrations.AddField(
            model_name='phieunhapchiphimuahangmodel',
            name='ma_ct',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã chứng từ'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='dia_chi',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Địa chỉ'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ghi_chu',
            field=models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='<PERSON><PERSON> chú'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_bp',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.bophanmodel', verbose_name='Bộ phận'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_cp0',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.chiphikhonghoplemodel', verbose_name='Chi phí không hợp lệ'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_dtt',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.dotthanhtoanmodel', verbose_name='Đợt thanh toán'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_hd',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.contractmodel', verbose_name='Hợp đồng'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_kh9',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang_cuc_thue', to='django_ledger.customermodel', verbose_name='Cục thuế'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_ku',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.kheuocmodel', verbose_name='Khế ước'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_mau_bc',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mẫu báo cáo'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_mau_ct',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.mausohdmodel', verbose_name='Mẫu số hóa đơn'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_phi',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.phimodel', verbose_name='Phí'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_so_thue',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã số thuế'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_sp',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.vattumodel', verbose_name='Sản phẩm'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_tc_thue',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Mã tính chất'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_tt',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.hanthanhtoanmodel', verbose_name='Mã thanh toán'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_vv',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang', to='django_ledger.vuviecmodel', verbose_name='Vụ việc'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ten_vt_thue',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Tên hàng hóa dịch vụ'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='thue',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=18, null=True, verbose_name='Thuế'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='thue_tes',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=18, null=True, verbose_name='Thuế TES'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='tien_hang',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=18, null=True, verbose_name='Tiền hàng'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='tien_hang_tes',
            field=models.DecimalField(blank=True, decimal_places=2, default=0, max_digits=18, null=True, verbose_name='Tiền hàng TES'),
        ),
        migrations.AddField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='tk_thue',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang_tk_thue', to='django_ledger.accountmodel', verbose_name='Tài khoản thuế'),
        ),
        migrations.AlterField(
            model_name='thuephieunhapchiphimuahangmodel',
            name='ma_thue',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='thue_phieu_nhap_chi_phi_mua_hang_ma_thue', to='django_ledger.taxmodel', verbose_name='Mã thuế'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_mau_ct'], name='thue_phieu__ma_mau__aeceec_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['tk_thue'], name='thue_phieu__tk_thue_35fd77_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_kh9'], name='thue_phieu__ma_kh9__e2db1c_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_tt'], name='thue_phieu__ma_tt_i_e27137_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_bp'], name='thue_phieu__ma_bp_i_e91b3d_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_vv'], name='thue_phieu__ma_vv_i_29193b_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_hd'], name='thue_phieu__ma_hd_i_47c555_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_dtt'], name='thue_phieu__ma_dtt__776847_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_ku'], name='thue_phieu__ma_ku_i_e10a86_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_phi'], name='thue_phieu__ma_phi__052db3_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_sp'], name='thue_phieu__ma_sp_i_0926d2_idx'),
        ),
        migrations.AddIndex(
            model_name='thuephieunhapchiphimuahangmodel',
            index=models.Index(fields=['ma_cp0'], name='thue_phieu__ma_cp0__e9ed30_idx'),
        ),
    ]
