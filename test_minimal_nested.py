#!/usr/bin/env python3
"""
Minimal test for nested data creation
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://127.0.0.1:8003"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "tutimi"

# Create basic auth header
auth_string = f"{USERNAME}:{PASSWORD}"
auth_bytes = auth_string.encode('ascii')
auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

headers = {
    'Authorization': f'Basic {auth_b64}',
    'Content-Type': 'application/json'
}

def test_minimal_nested():
    """Test with minimal required fields only"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    data = {
        "ma_gd": "GD001",
        "pc_tao_yn": "N",
        "ma_httt": "TM",
        "loai_ck": "PT",
        "ck_tl_nt": 0.0,
        "ma_so_thue": "0123456789",
        "du_cn_thu": 0.0,
        "dia_chi": "123 Main Street",
        "ong_ba": "Nguyen Van A",
        "e_mail": "<EMAIL>",
        "tk": "3314d5f7-4cb9-4253-8833-525dcd23801f",
        "dien_giai": "Minimal test",
        "id": "ID001",
        "unit_id": "7043926df26f463299c344f2c13ac6e4",
        "ma_nk": "c1a10a51-9d62-4b6a-8c62-76432af3ca03",
        "so_ct": "MDV1.01.25.000019",
        "i_so_ct": 19,
        "ngay_ct": "2025-01-02",
        "ngay_lct": "2025-01-02",
        "so_ct0": "HD001",
        "so_ct2": "HD001-2",
        "ngay_ct0": "2025-01-02",
        "ty_gia": 1.0,
        "status": "1",
        "transfer_yn": "N",
        "ma_ngv": "NGV001",
        "t_thue_nt": 0.0,
        "t_thue": 0.0,
        "t_tien_nt": 1000.0,
        "t_tien": 1000.0,
        "t_ck_nt_ex": 0.0,
        "t_ck_ex": 0.0,
        "t_ck_nt": 0.0,
        "t_ck": 0.0,
        "t_tt_nt": 1000.0,
        "t_tt": 1000.0,
        "chi_tiet": [
            {
                "line": 1,
                "ma_dv": "DV001",
                "x_new_item": "N",
                "tk_vt": "6411",
                "ten_tk": "Chi phi dich vu",
                "dien_giai": "Minimal chi tiet",
                "ma_lts": "LTS001",
                "dvt": "Gio",
                "so_luong": 1.0,
                "gia_nt": 1000.0,
                "tien_nt": 1000.0,
                "tien_tck_nt": 1000.0,
                "tl_ck": 0.0,
                "ck_nt": 0.0,
                "gia": 1000.0,
                "tien": 1000.0,
                "tien_tck": 1000.0,
                "ck": 0.0,
                "ma_thue": "VAT10",
                "thue_suat": 0.0,
                "thue_nt": 0.0,
                "thue": 0.0
            }
        ]
        # NO thue array for now
    }

    print("Testing MINIMAL NESTED CREATE...")
    print(f"URL: {url}")

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 201:
            result = response.json()
            print("✅ MINIMAL NESTED CREATE test PASSED")
            print(f"UUID: {result.get('uuid')}")
            print(f"chi_tiet_data count: {len(result.get('chi_tiet_data', []))}")
            print(f"thue_data count: {len(result.get('thue_data', []))}")
            return result
        else:
            print(f"Response: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
            print("❌ MINIMAL NESTED CREATE test FAILED")
            return None

    except Exception as e:
        print(f"❌ MINIMAL NESTED CREATE test ERROR: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Starting Minimal Nested Test...")
    result = test_minimal_nested()

    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
