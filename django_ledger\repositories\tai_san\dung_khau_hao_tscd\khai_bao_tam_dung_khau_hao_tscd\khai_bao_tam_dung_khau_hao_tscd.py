"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) repository implementation.  # noqa: E501
"""

from typing import Any, Dict, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db import IntegrityError  # noqa: F401
from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models import EntityModel  # noqa: F401,
from django_ledger.models.tai_san.dung_khau_hao_tscd.khai_bao_tam_dung_khau_hao_tscd import (  # noqa: F401,
    KhaiBaoTamDungKhauHaoTSCDModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class KhaiBaoTamDungKhauHaoTSCDRepository(BaseRepository):
    """
    Repository class for KhaiBaoTamDungKhauHaoTSCDModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=KhaiBaoTamDungKhauHaoTSCDModel)

    def get_queryset(self) -> QuerySet:  # noqa: C901
        """
        Returns the base queryset for KhaiBaoTamDungKhauHaoTSCDModel.

        Returns
        -------
        QuerySet
            The base queryset for KhaiBaoTamDungKhauHaoTSCDModel.
        """
        return self.model_class.objects.all().select_related(
            'entity_model', 'ma_ts'
        )

    def get_by_id(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> Optional[KhaiBaoTamDungKhauHaoTSCDModel]:  # noqa: C901
        """
        Retrieves a KhaiBaoTamDungKhauHaoTSCDModel by its UUID.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to retrieve.

        Returns
        -------
        Optional[KhaiBaoTamDungKhauHaoTSCDModel]
            The KhaiBaoTamDungKhauHaoTSCDModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.for_entity(
                entity_slug=entity_slug
            ).get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list(self, entity_slug: str, **kwargs) -> QuerySet:  # noqa: C901
        """
        Lists KhaiBaoTamDungKhauHaoTSCDModel instances for a specific entity.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        **kwargs : dict
            Additional filter parameters.

        Returns
        -------
        QuerySet
            A QuerySet of KhaiBaoTamDungKhauHaoTSCDModel instances.
        """
        return self.model_class.objects.for_entity(
            entity_slug=entity_slug
        ).filter(**kwargs)

    def create(
        self, entity_slug: str, data: Dict[str, Any]
    ) -> KhaiBaoTamDungKhauHaoTSCDModel:  # noqa: C901
        """
        Creates a new KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        data : Dict[str, Any]
            The data for the new KhaiBaoTamDungKhauHaoTSCDModel.

        Returns
        -------
        KhaiBaoTamDungKhauHaoTSCDModel
            The created KhaiBaoTamDungKhauHaoTSCDModel instance.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get entity model
        entity_model = get_object_or_404(EntityModel, slug=entity_slug)
        # Add entity model to data
        data['entity_model'] = entity_model
        # Create the KhaiBaoTamDungKhauHaoTSCDModel instance
        instance = self.model_class(**data)
        try:
            instance.save()
        except IntegrityError as e:
            if 'unique_ma_ts_ngay_hl_tu_khai_bao_tam_dung_khau_hao_tscd' in str(e):
                raise ValueError("Tài sản này đã có khai báo tạm dừng khấu hao. Vui lòng chọn tài sản khác.")
            raise e

        return instance

    def update(  # noqa: C901
        self, entity_slug: str, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[KhaiBaoTamDungKhauHaoTSCDModel]:
        """
        Updates an existing KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to update.
        data : Dict[str, Any]
            The data to update the KhaiBaoTamDungKhauHaoTSCDModel with.

        Returns
        -------
        Optional[KhaiBaoTamDungKhauHaoTSCDModel]
            The updated KhaiBaoTamDungKhauHaoTSCDModel instance, or None if not found.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            # Convert UUIDs to model instances
            data = self.convert_uuids_to_model_instances(data)
            for key, value in data.items():
                setattr(instance, key, value)
            try:
                instance.save()
            except IntegrityError as e:
                if 'unique_ma_ts_ngay_hl_tu_khai_bao_tam_dung_khau_hao_tscd' in str(e):
                    raise ValueError("Tài sản này đã có khai báo tạm dừng khấu hao. Vui lòng chọn tài sản khác.")
                raise e
            return instance
        return None

    def delete(
        self, entity_slug: str, uuid: Union[str, UUID]
    ) -> bool:  # noqa: C901
        """
        Deletes a KhaiBaoTamDungKhauHaoTSCDModel instance.

        Parameters
        ----------
        entity_slug : str
            The entity slug.
        uuid : Union[str, UUID]
            The UUID of the KhaiBaoTamDungKhauHaoTSCDModel to delete.

        Returns
        -------
        bool
            True if the KhaiBaoTamDungKhauHaoTSCDModel was deleted, False otherwise.
        """
        instance = self.get_by_id(entity_slug=entity_slug, uuid=uuid)
        if instance:
            instance.delete()
            return True
        return False

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Override BaseRepository method to handle specific field conversions for this model.  # noqa: E501

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings.

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances.
        """
        # First call the parent method to handle general conversions
        data_copy = super().convert_uuids_to_model_instances(data)
        # Handle specific conversion for ma_ts field
        if 'ma_ts' in data_copy and data_copy['ma_ts']:
            try:
                # Import here to avoid circular imports
                from uuid import UUID  # noqa: F401,

                from django_ledger.models import (  # noqa: F401,
                    KhaiBaoThongTinTaiSanCoDinhModel,
                )

                ma_ts_value = str(data_copy['ma_ts']).strip()

                # Try to parse as UUID first
                try:
                    uuid_obj = UUID(ma_ts_value)
                    # Get the KhaiBaoThongTinTaiSanCoDinhModel instance by UUID
                    data_copy['ma_ts'] = (
                        KhaiBaoThongTinTaiSanCoDinhModel.objects.get(uuid=uuid_obj)
                    )
                except ValueError:
                    # If not a valid UUID, try to find by ma_ts field (asset code)
                    # Extract the asset code from display name (e.g., "TSCD001 - May tinh...")
                    asset_code = ma_ts_value.split(' - ')[0].strip() if ' - ' in ma_ts_value else ma_ts_value

                    try:
                        data_copy['ma_ts'] = (
                            KhaiBaoThongTinTaiSanCoDinhModel.objects.get(ma_ts=asset_code)
                        )
                    except KhaiBaoThongTinTaiSanCoDinhModel.DoesNotExist:
                        # If still not found, raise error with helpful message
                        raise ValueError(f"Không tìm thấy tài sản với mã '{asset_code}'. Vui lòng kiểm tra lại mã tài sản.")

            except KhaiBaoThongTinTaiSanCoDinhModel.DoesNotExist:
                # If model doesn't exist by UUID, raise a specific error
                raise ValueError(f"Tài sản với UUID {data_copy['ma_ts']} không tồn tại")
            except Exception as e:
                # If any other error, raise a general error
                raise ValueError(f"Lỗi khi xử lý mã tài sản: {str(e)}")
        return data_copy
