"""
Django Ledger created by <PERSON> <msan<PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

KhaiBaoTamDungKhauHaoTSCD (Fixed Asset Depreciation Suspension Declaration) model implementation.  # noqa: E501
"""

import uuid as uuid_lib  # noqa: F401
from datetime import date  # noqa: F401
from uuid import uuid4  # noqa: F401

from django.db import models  # noqa: F401,
from django.db.models import Manager, QuerySet  # noqa: F401,
from django.utils.translation import gettext_lazy as _  # noqa: F401,

from django_ledger.models.mixins import CreateUpdateMixIn  # noqa: F401,


class KhaiBaoTamDungKhauHaoTSCDModelQueryset(QuerySet):
    """
    A custom defined QuerySet for the KhaiBaoTamDungKhauHaoTSCDModel.
    """

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Fetches a QuerySet of KhaiBaoTamDungKhauHaoTSCDModels for a specific entity.

        Parameters
        __________
        entity_slug: str
            The entity slug to filter by.
        """
        return self.filter(entity_model__slug__exact=entity_slug)

    def active(self):  # noqa: C901
        """
        Returns a QuerySet of active KhaiBaoTamDungKhauHaoTSCDModel instances.
        """
        return self.all()


class KhaiBaoTamDungKhauHaoTSCDModelManager(Manager):
    """
    A custom defined KhaiBaoTamDungKhauHaoTSCDModel Manager that will act as an interface to handling the DB queries to the  # noqa: E501
    KhaiBaoTamDungKhauHaoTSCDModel.
    """

    def get_queryset(self):  # noqa: C901
        """
        Returns the custom KhaiBaoTamDungKhauHaoTSCDModelQueryset.
        """
        return KhaiBaoTamDungKhauHaoTSCDModelQueryset(
            self.model, using=self._db
        )

    def for_entity(self, entity_slug):  # noqa: C901
        """
        Returns KhaiBaoTamDungKhauHaoTSCDModel for a specific entity.

        Parameters
        ----------
        entity_slug: str
            The entity slug to filter by.

        Returns
        -------
        KhaiBaoTamDungKhauHaoTSCDModelQueryset
            A QuerySet of KhaiBaoTamDungKhauHaoTSCDModel with applied filters.
        """
        return self.get_queryset().for_entity(entity_slug=entity_slug)


class KhaiBaoTamDungKhauHaoTSCDModelAbstract(CreateUpdateMixIn):
    """
    This is the main abstract class which the KhaiBaoTamDungKhauHaoTSCDModel database will inherit from.  # noqa: E501
    The KhaiBaoTamDungKhauHaoTSCDModel inherits functionality from the following MixIns:

        1. :func:`CreateUpdateMixIn <django_ledger.models.mixins.CreateUpdateMixIn>`

    Attributes
    __________
    uuid : UUID
        This is a unique primary key generated for the table. The default value of this field is uuid_lib.uuid4().  # noqa: E501

    entity_model : EntityModel
        The entity that this fixed asset depreciation suspension declaration belongs to.

    ma_ts : KhaiBaoThongTinTaiSanCoDinhModel
        The fixed asset code.

    ngay_hl_tu : date
        The effective date from.

    ngay_hl_den : date
        The effective date to.

    """

    uuid = models.UUIDField(
        primary_key=True,
        default=uuid_lib.uuid4,
        editable=False,
        verbose_name=_("UUID"),
        help_text=_("Unique identifier for the record"),
    )
    entity_model = models.ForeignKey(
        'django_ledger.EntityModel',
        on_delete=models.CASCADE,
        verbose_name=_('Entity Model'),
        related_name='khai_bao_tam_dung_khau_hao_tscd',
        help_text=_(
            "Entity that this fixed asset depreciation suspension declaration belongs to"
        ),  # noqa: E501
    )
    ma_ts = models.ForeignKey(
        'django_ledger.KhaiBaoThongTinTaiSanCoDinhModel',
        on_delete=models.CASCADE,
        verbose_name=_('Mã tài sản'),
        related_name='khai_bao_tam_dung_khau_hao_tscd',
        help_text=_("Fixed asset that this depreciation suspension applies to"),
    )
    ngay_hl_tu = models.DateField(
        null=False,
        blank=False,
        verbose_name=_("Ngày hiệu lực từ"),
        help_text=_("Effective date from"),
    )
    ngay_hl_den = models.DateField(
        null=True,
        blank=True,
        verbose_name=_("Ngày hiệu lực đến"),
        help_text=_("Effective date to"),
    )

    objects = KhaiBaoTamDungKhauHaoTSCDModelManager.from_queryset(
        KhaiBaoTamDungKhauHaoTSCDModelQueryset
    )()

    class Meta:
        abstract = True
        verbose_name = _('Khai báo tạm dừng khấu hao TSCĐ')
        verbose_name_plural = _('Khai báo tạm dừng khấu hao TSCĐ')
        indexes = [
            models.Index(fields=['entity_model']),
            models.Index(fields=['ma_ts']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['ma_ts', 'ngay_hl_tu'],
                name='unique_ma_ts_ngay_hl_tu_khai_bao_tam_dung_khau_hao_tscd'
            )
        ]

    def __str__(self):  # noqa: C901
        return f'{self.ma_ts} - {self.ngay_hl_tu} to {self.ngay_hl_den}'


class KhaiBaoTamDungKhauHaoTSCDModel(KhaiBaoTamDungKhauHaoTSCDModelAbstract):
    """
    Base Fixed Asset Depreciation Suspension Declaration Model Implementation
    """

    class Meta(KhaiBaoTamDungKhauHaoTSCDModelAbstract.Meta):
        abstract = False
        db_table = 'khai_bao_tam_dung_khau_hao_tscd'
