#!/usr/bin/env python3
"""
Check real UUIDs that exist in database
"""

import sqlite3

def check_real_uuids():
    """Check what UUIDs actually exist in database."""
    
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    try:
        # Check entity
        cursor.execute("SELECT uuid, slug FROM django_ledger_entitymodel WHERE slug = 'tutimi-dnus2xnc'")
        entity = cursor.fetchone()
        if entity:
            print(f"✅ Entity: {entity[0]} ({entity[1]})")
            entity_uuid = entity[0].replace('-', '')
        else:
            print("❌ Entity not found")
            return
        
        # Check vendors
        print("\n📋 VENDORS:")
        cursor.execute("SELECT uuid, vendor_name FROM django_ledger_vendormodel LIMIT 10")
        vendors = cursor.fetchall()
        for v in vendors:
            print(f"   {v[0]} - {v[1]}")
        
        # Check customers (might be used as vendors)
        print("\n📋 CUSTOMERS:")
        cursor.execute("SELECT uuid, customer_name FROM django_ledger_customermodel LIMIT 10")
        customers = cursor.fetchall()
        for c in customers:
            print(f"   {c[0]} - {c[1]}")
        
        # Check accounts
        print("\n📋 ACCOUNTS:")
        cursor.execute("SELECT uuid, code, name FROM django_ledger_accountmodel WHERE code IS NOT NULL ORDER BY code LIMIT 15")
        accounts = cursor.fetchall()
        for a in accounts:
            print(f"   {a[0]} - {a[1]} ({a[2]})")
        
        # Check currencies
        print("\n📋 CURRENCIES:")
        cursor.execute("SELECT uuid, code, name FROM django_ledger_currencymodel LIMIT 5")
        currencies = cursor.fetchall()
        for c in currencies:
            print(f"   {c[0]} - {c[1]} ({c[2]})")
        
        # Check entity units
        print("\n📋 ENTITY UNITS:")
        cursor.execute("SELECT uuid, name FROM django_ledger_entityunitmodel LIMIT 5")
        units = cursor.fetchall()
        for u in units:
            print(f"   {u[0]} - {u[1]}")
        
        # Check staff/employees
        print("\n📋 STAFF/EMPLOYEES:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%staff%' OR name LIKE '%employee%' OR name LIKE '%nhan_vien%'")
        staff_tables = cursor.fetchall()
        print(f"Staff tables: {[t[0] for t in staff_tables]}")
        
        # Try to find any table with ma_nv or similar
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = cursor.fetchall()
        for table in all_tables:
            table_name = table[0]
            try:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [c[1] for c in columns]
                if any('ma_nv' in col or 'staff' in col.lower() or 'employee' in col.lower() for col in column_names):
                    print(f"   Found staff-like table: {table_name} with columns: {column_names}")
                    cursor.execute(f"SELECT uuid FROM {table_name} LIMIT 3")
                    staff_uuids = cursor.fetchall()
                    for s in staff_uuids:
                        print(f"      {s[0]}")
            except:
                pass
        
        # Check payment terms
        print("\n📋 PAYMENT TERMS:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name LIKE '%payment%' OR name LIKE '%thanh_toan%' OR name LIKE '%ma_tt%')")
        payment_tables = cursor.fetchall()
        print(f"Payment tables: {[t[0] for t in payment_tables]}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_real_uuids()
