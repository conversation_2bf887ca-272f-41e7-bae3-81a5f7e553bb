#!/usr/bin/env python3
"""
Comprehensive test for all HoaDonMuaDichVu APIs
"""

import requests
import json
import base64

# Configuration
BASE_URL = "http://127.0.0.1:8003"
ENTITY_SLUG = "tutimi-dnus2xnc"
USERNAME = "tutimi"
PASSWORD = "tutimi"

# Create basic auth header
auth_string = f"{USERNAME}:{PASSWORD}"
auth_bytes = auth_string.encode('ascii')
auth_b64 = base64.b64encode(auth_bytes).decode('ascii')

headers = {
    'Authorization': f'Basic {auth_b64}',
    'Content-Type': 'application/json'
}

def test_list_api():
    """Test LIST API"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ LIST API: {result['count']} records found")
            return True
        else:
            print(f"❌ LIST API failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ LIST API error: {e}")
        return False

def test_create_basic():
    """Test CREATE API without nested data"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    data = {
        "ma_gd": "TEST001",
        "dien_giai": "Test basic create",
        "tk": "3314d5f7-4cb9-4253-8833-525dcd23801f",
        "unit_id": "7043926df26f463299c344f2c13ac6e4",
        "ma_nk": "c1a10a51-9d62-4b6a-8c62-76432af3ca03",
        "so_ct": "MDV1.01.25.001200",
        "i_so_ct": 1200,
        "ngay_ct": "2025-01-02",
        "ngay_lct": "2025-01-02"
    }

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        if response.status_code == 201:
            result = response.json()
            print(f"✅ CREATE BASIC: {result['uuid']}")
            return result['uuid']
        else:
            try:
                error_detail = response.json()
                print(f"❌ CREATE BASIC failed: {response.status_code} - {error_detail}")
            except:
                print(f"❌ CREATE BASIC failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ CREATE BASIC error: {e}")
        return None

def test_create_nested():
    """Test CREATE API with nested data"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/"

    data = {
        "ma_gd": "TEST002",
        "dien_giai": "Test nested create",
        "tk": "3314d5f7-4cb9-4253-8833-525dcd23801f",
        "unit_id": "7043926df26f463299c344f2c13ac6e4",
        "ma_nk": "c1a10a51-9d62-4b6a-8c62-76432af3ca03",
        "so_ct": "MDV1.01.25.001201",
        "i_so_ct": 1201,
        "ngay_ct": "2025-01-02",
        "ngay_lct": "2025-01-02",
        "chi_tiet": [
            {
                "line": 1,
                "ma_dv": "DV001",
                "x_new_item": "N",
                "tk_vt": "6411",
                "ten_tk": "Chi phi dich vu",
                "dien_giai": "Test chi tiet",
                "ma_lts": "LTS001",
                "dvt": "Gio",
                "so_luong": 1.0,
                "gia_nt": 1000.0,
                "tien_nt": 1000.0,
                "tien_tck_nt": 1000.0,
                "tl_ck": 0.0,
                "ck_nt": 0.0,
                "gia": 1000.0,
                "tien": 1000.0,
                "tien_tck": 1000.0,
                "ck": 0.0,
                "ma_thue": "VAT10",
                "thue_suat": 0.0,
                "thue_nt": 0.0,
                "thue": 0.0
            }
        ],
        "thue": [
            {
                "line": 1,
                "i_so_ct": "001",
                "line_ct": 1,
                "ma_ts": "TS001",
                "ma_lts": "LTS001",
                "ngay_mua": "2025-01-02",
                "ngay_kh0": "2025-01-02",
                "so_ky_kh": 60,
                "ngay_kh_kt": "2030-01-02",
                "nguyen_gia_nt": 1000.0,
                "gt_da_kh_nt": 0.0,
                "gt_cl_nt": 1000.0,
                "gt_kh_ky_nt": 16.67,
                "nguyen_gia": 1000.0,
                "gt_da_kh": 0.0,
                "gt_cl": 1000.0,
                "gt_kh_ky": 16.67,
                "so_hieu_ts": "TS001-SN",
                "tk_ts": "2111",
                "tk_kh": "2118",
                "tk_cp": "6427"
            }
        ]
    }

    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        if response.status_code == 201:
            result = response.json()
            print(f"✅ CREATE NESTED: {result['uuid']} (chi_tiet: {len(result.get('chi_tiet_data', []))}, thue: {len(result.get('thue_data', []))})")
            return result['uuid']
        else:
            print(f"❌ CREATE NESTED failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ CREATE NESTED error: {e}")
        return None

def test_retrieve(uuid):
    """Test RETRIEVE API"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{uuid}/"

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ RETRIEVE: {result['ma_gd']} - {result['dien_giai']}")
            return True
        else:
            print(f"❌ RETRIEVE failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ RETRIEVE error: {e}")
        return False

def test_delete(uuid):
    """Test DELETE API"""
    url = f"{BASE_URL}/api/entities/{ENTITY_SLUG}/erp/mua-hang/hoa-don-mua-vao/hoa-don-mua-dich-vu/{uuid}/"

    try:
        response = requests.delete(url, headers=headers, timeout=30)
        if response.status_code == 204:
            print(f"✅ DELETE: {uuid}")
            return True
        else:
            try:
                error_detail = response.json()
                print(f"❌ DELETE failed: {response.status_code} - {error_detail}")
            except:
                print(f"❌ DELETE failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ DELETE error: {e}")
        return False

def main():
    """Run comprehensive tests"""
    print("🚀 Starting Comprehensive HoaDonMuaDichVu API Tests...")
    print("=" * 60)

    results = []

    # Test LIST
    print("1. Testing LIST API...")
    results.append(test_list_api())

    # Test CREATE BASIC
    print("\n2. Testing CREATE BASIC API...")
    basic_uuid = test_create_basic()
    results.append(basic_uuid is not None)

    # Test CREATE NESTED
    print("\n3. Testing CREATE NESTED API...")
    nested_uuid = test_create_nested()
    results.append(nested_uuid is not None)

    # Test RETRIEVE
    if nested_uuid:
        print("\n4. Testing RETRIEVE API...")
        results.append(test_retrieve(nested_uuid))

    # Test DELETE
    if basic_uuid:
        print("\n5. Testing DELETE API (basic)...")
        results.append(test_delete(basic_uuid))

    if nested_uuid:
        print("\n6. Testing DELETE API (nested)...")
        results.append(test_delete(nested_uuid))

    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    print(f"📊 SUMMARY: {passed}/{total} tests passed ({passed/total*100:.1f}%)")

    if passed == total:
        print("🎉 ALL TESTS PASSED! API is 100% functional!")
    else:
        print("⚠️ Some tests failed. Please check the issues above.")

    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
