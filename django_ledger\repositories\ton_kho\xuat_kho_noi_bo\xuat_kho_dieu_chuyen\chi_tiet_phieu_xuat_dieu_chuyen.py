"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

ChiTietPhieuXuatDieuChuyen (Warehouse Transfer Detail) repository implementation.
"""

from typing import Any, Dict, List, Optional, Union  # noqa: F401
from uuid import UUID  # noqa: F401

from django.db.models import QuerySet  # noqa: F401
from django.shortcuts import get_object_or_404  # noqa: F401,

from django_ledger.models.ton_kho.xuat_kho_noi_bo.xuat_kho_dieu_chuyen import (  # noqa: F401,
    ChiTietPhieuXuatDieuChuyenModel,
    PhieuXuatDieuChuyenModel,
)
from django_ledger.repositories.base import BaseRepository  # noqa: F401,


class ChiTietPhieuXuatDieuChuyenRepository(BaseRepository):
    """
    Repository class for ChiTietPhieuXuatDieuChuyenModel.
    Handles database operations for the model.
    """

    def __init__(self):  # noqa: C901
        super().__init__(model_class=ChiTietPhieuXuatDieuChuyenModel)

    def get_by_id(
        self, uuid: Union[str, UUID]
    ) -> Optional[ChiTietPhieuXuatDieuChuyenModel]:  # noqa: C901
        """
        Retrieves a ChiTietPhieuXuatDieuChuyenModel by its UUID.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatDieuChuyenModel to retrieve.

        Returns
        -------
        Optional[ChiTietPhieuXuatDieuChuyenModel]
            The ChiTietPhieuXuatDieuChuyenModel with the given UUID, or None if not found.  # noqa: E501
        """
        try:
            return self.model_class.objects.get(uuid=uuid)
        except self.model_class.DoesNotExist:
            return None

    def list_for_phieu_xuat_dieu_chuyen(
        self, phieu_xuat_dieu_chuyen_id: Union[str, UUID]
    ) -> QuerySet:  # noqa: C901
        """
        Lists all ChiTietPhieuXuatDieuChuyenModel instances for a specific warehouse transfer.  # noqa: E501

        Parameters
        ----------
        phieu_xuat_dieu_chuyen_id : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel.

        Returns
        -------
        QuerySet
            A QuerySet of ChiTietPhieuXuatDieuChuyenModel instances.
        """
        return self.model_class.objects.for_phieu_xuat_dieu_chuyen(
            phieu_xuat_dieu_chuyen_id=phieu_xuat_dieu_chuyen_id
        )

    def create(
        self, phieu_xuat_dieu_chuyen_id: Union[str, UUID], data: Dict[str, Any]
    ) -> ChiTietPhieuXuatDieuChuyenModel:  # noqa: C901
        """
        Creates a new ChiTietPhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        phieu_xuat_dieu_chuyen_id : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel.
        data : Dict[str, Any]
            The data for the new ChiTietPhieuXuatDieuChuyenModel.

        Returns
        -------
        ChiTietPhieuXuatDieuChuyenModel
            The created ChiTietPhieuXuatDieuChuyenModel instance.
        """
        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Get phieu_xuat_dieu_chuyen model
        phieu_xuat_dieu_chuyen = get_object_or_404(
            PhieuXuatDieuChuyenModel, uuid=phieu_xuat_dieu_chuyen_id
        )

        # Add phieu_xuat_dieu_chuyen to data
        data['phieu_xuat'] = phieu_xuat_dieu_chuyen
        # Create the ChiTietPhieuXuatDieuChuyenModel instance
        instance = self.model_class(**data)
        instance.save()

        return instance

    def create_many(  # noqa: C901
        self,
        phieu_xuat_dieu_chuyen_id: Union[str, UUID],
        data_list: List[Dict[str, Any]],
    ) -> List[ChiTietPhieuXuatDieuChuyenModel]:
        """
        Creates multiple ChiTietPhieuXuatDieuChuyenModel instances.

        Parameters
        ----------
        phieu_xuat_dieu_chuyen_id : Union[str, UUID]
            The UUID of the PhieuXuatDieuChuyenModel.
        data_list : List[Dict[str, Any]]
            A list of data dictionaries for the new ChiTietPhieuXuatDieuChuyenModel instances.  # noqa: E501

        Returns
        -------
        List[ChiTietPhieuXuatDieuChuyenModel]
            A list of created ChiTietPhieuXuatDieuChuyenModel instances.
        """
        instances = []
        for data in data_list:
            instance = self.create(
                phieu_xuat_dieu_chuyen_id=phieu_xuat_dieu_chuyen_id,
                data=data,
            )
            instances.append(instance)

        return instances

    def update(
        self, uuid: Union[str, UUID], data: Dict[str, Any]
    ) -> Optional[ChiTietPhieuXuatDieuChuyenModel]:  # noqa: C901
        """
        Updates an existing ChiTietPhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatDieuChuyenModel to update.
        data : Dict[str, Any]
            The data to update the ChiTietPhieuXuatDieuChuyenModel with.

        Returns
        -------
        Optional[ChiTietPhieuXuatDieuChuyenModel]
            The updated ChiTietPhieuXuatDieuChuyenModel instance, or None if not found.
        """
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            return None

        # Convert UUIDs to model instances
        data = self.convert_uuids_to_model_instances(data)
        # Update the instance
        for key, value in data.items():
            setattr(instance, key, value)

        instance.save()

        return instance

    def delete(self, uuid: Union[str, UUID]) -> bool:  # noqa: C901
        """
        Deletes a ChiTietPhieuXuatDieuChuyenModel instance.

        Parameters
        ----------
        uuid : Union[str, UUID]
            The UUID of the ChiTietPhieuXuatDieuChuyenModel to delete.

        Returns
        -------
        bool
            True if the instance was deleted, False otherwise.
        """
        instance = self.get_by_id(uuid=uuid)
        if not instance:
            return False

        instance.delete()
        return True

    def convert_uuids_to_model_instances(
        self, data: Dict[str, Any]
    ) -> Dict[str, Any]:  # noqa: C901
        """
        Convert UUID strings to model instances for foreign key fields.
        This method extends the base implementation to specifically handle
        ChiTietPhieuXuatDieuChuyen related fields and other foreign keys.

        Parameters
        ----------
        data : Dict[str, Any]
            The data containing UUID strings

        Returns
        -------
        Dict[str, Any]
            The data with UUID strings converted to model instances
        """
        # Create a copy and exclude CharField fields that should not be converted
        data_copy = data.copy()

        # Fields that are CharField and should NOT be converted to model instances
        char_fields = [
            'line',
            'ma_lo',
            'ghi_chu',
            'ma_lsx',
            'so_ct_pn',
        ]

        # Temporarily remove CharField fields to prevent conversion
        char_field_values = {}
        for field in char_fields:
            if field in data_copy:
                char_field_values[field] = data_copy.pop(field)

        # Use the base implementation to handle common patterns (excluding CharField fields)
        data_copy = super().convert_uuids_to_model_instances(data_copy)

        # Restore CharField fields with their original values
        data_copy.update(char_field_values)

        return data_copy
